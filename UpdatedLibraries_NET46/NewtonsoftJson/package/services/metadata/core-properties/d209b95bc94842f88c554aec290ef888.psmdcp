<?xml version="1.0" encoding="utf-8"?>
<coreProperties xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.openxmlformats.org/package/2006/metadata/core-properties">
  <dc:creator><PERSON></dc:creator>
  <dc:description>Json.NET is a popular high-performance JSON framework for .NET</dc:description>
  <dc:identifier>Newtonsoft.Json</dc:identifier>
  <version>13.0.3</version>
  <keywords>json</keywords>
  <lastModifiedBy>NuGet.Build.Tasks.Pack, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35;Microsoft Windows NT 10.0.20348.0;.NET Framework 4.7.2</lastModifiedBy>
</coreProperties>