﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Npgsql</id>
    <version>6.0.10</version>
    <authors><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON></authors>
    <license type="expression">PostgreSQL</license>
    <licenseUrl>https://licenses.nuget.org/PostgreSQL</licenseUrl>
    <icon>postgresql.png</icon>
    <readme>README.md</readme>
    <projectUrl>https://github.com/npgsql/npgsql</projectUrl>
    <description>Npgsql is the open source .NET data provider for PostgreSQL.</description>
    <copyright>Copyright 2021 © The Npgsql Development Team</copyright>
    <tags>npgsql postgresql postgres ado ado.net database sql</tags>
    <repository type="git" url="https://github.com/npgsql/npgsql" commit="35cc5b5e11d3e6c150882547e029ee249574e499" />
    <dependencies>
      <group targetFramework=".NETCoreApp3.1">
        <dependency id="System.Diagnostics.DiagnosticSource" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net5.0">
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net6.0">
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.0">
        <dependency id="Microsoft.Bcl.HashCode" version="1.1.1" exclude="Build,Analyzers" />
        <dependency id="System.Collections.Immutable" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Diagnostics.DiagnosticSource" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Text.Json" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Threading.Channels" version="6.0.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.1">
        <dependency id="System.Collections.Immutable" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Diagnostics.DiagnosticSource" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Text.Json" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Threading.Channels" version="6.0.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>