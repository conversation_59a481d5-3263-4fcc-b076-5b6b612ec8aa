<?xml version="1.0" encoding="utf-8"?>
<coreProperties xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.openxmlformats.org/package/2006/metadata/core-properties">
  <dc:creator>Microsoft</dc:creator>
  <dc:description>Provides the IAsyncEnumerable&lt;T&gt; and IAsyncDisposable interfaces and helper types for .NET Standard 2.0. This package is not required starting with .NET Standard 2.1 and .NET Core 3.0.

Commonly Used Types:
System.IAsyncDisposable
System.Collections.Generic.IAsyncEnumerable
System.Collections.Generic.IAsyncEnumerator
 
When using NuGet 3.x this package requires at least version 3.4.</dc:description>
  <dc:identifier>Microsoft.Bcl.AsyncInterfaces</dc:identifier>
  <version>1.1.0</version>
  <keywords></keywords>
  <lastModifiedBy>NuGet.Packaging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35;</lastModifiedBy>
</coreProperties>