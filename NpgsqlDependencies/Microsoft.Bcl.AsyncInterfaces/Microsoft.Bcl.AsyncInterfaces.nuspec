﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata minClientVersion="2.12">
    <id>Microsoft.Bcl.AsyncInterfaces</id>
    <version>1.1.0</version>
    <title>Microsoft.Bcl.AsyncInterfaces</title>
    <authors>Microsoft</authors>
    <owners>microsoft,dotnetframework</owners>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <projectUrl>https://github.com/dotnet/corefx</projectUrl>
    <iconUrl>http://go.microsoft.com/fwlink/?LinkID=288859</iconUrl>
    <description>Provides the IAsyncEnumerable&lt;T&gt; and IAsyncDisposable interfaces and helper types for .NET Standard 2.0. This package is not required starting with .NET Standard 2.1 and .NET Core 3.0.

Commonly Used Types:
System.IAsyncDisposable
System.Collections.Generic.IAsyncEnumerable
System.Collections.Generic.IAsyncEnumerator
 
When using NuGet 3.x this package requires at least version 3.4.</description>
    <releaseNotes>https://go.microsoft.com/fwlink/?LinkID=799421</releaseNotes>
    <copyright>© Microsoft Corporation. All rights reserved.</copyright>
    <serviceable>true</serviceable>
    <dependencies>
      <group targetFramework=".NETFramework4.6.1">
        <dependency id="System.Threading.Tasks.Extensions" version="4.5.2" />
      </group>
      <group targetFramework=".NETCoreApp2.1" />
      <group targetFramework=".NETStandard2.0">
        <dependency id="System.Threading.Tasks.Extensions" version="4.5.2" />
      </group>
      <group targetFramework=".NETStandard2.1">
        <dependency id="System.Threading.Tasks.Extensions" version="4.5.2" />
      </group>
    </dependencies>
    <frameworkAssemblies>
      <frameworkAssembly assemblyName="mscorlib" targetFramework=".NETFramework4.6.1" />
    </frameworkAssemblies>
  </metadata>
</package>