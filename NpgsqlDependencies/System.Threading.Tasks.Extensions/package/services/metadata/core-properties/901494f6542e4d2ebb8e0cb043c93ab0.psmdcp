<?xml version="1.0" encoding="utf-8"?>
<coreProperties xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.openxmlformats.org/package/2006/metadata/core-properties">
  <dc:creator>Microsoft</dc:creator>
  <dc:description>Provides additional types that simplify the work of writing concurrent and asynchronous code.

Commonly Used Types:
System.Threading.Tasks.ValueTask&lt;TResult&gt;
 
637a8d58f72f2b0f1a71187530c3cf433e95a75a</dc:description>
  <dc:identifier>System.Threading.Tasks.Extensions</dc:identifier>
  <version>4.5.3</version>
  <keywords></keywords>
  <lastModifiedBy>NuGet.Packaging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35;Microsoft Windows NT 10.0.14393.0;.NET Framework 4.5</lastModifiedBy>
</coreProperties>