<?xml version="1.0" encoding="utf-8"?>
<coreProperties xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.openxmlformats.org/package/2006/metadata/core-properties">
  <dc:creator>Microsoft</dc:creator>
  <dc:description>Provides the System.ValueTuple structs, which implement the underlying types for tuples in C# and Visual Basic.

Commonly Used Types:
System.ValueTuple
System.ValueTuple&lt;T1&gt;
System.ValueTuple&lt;T1, T2&gt;
System.ValueTuple&lt;T1, T2, T3&gt;
System.ValueTuple&lt;T1, T2, T3, T4&gt;
System.ValueTuple&lt;T1, T2, T3, T4, T5&gt;
System.ValueTuple&lt;T1, T2, T3, T4, T5, T6&gt;
System.ValueTuple&lt;T1, T2, T3, T4, T5, T6, T7&gt;
System.ValueTuple&lt;T1, T2, T3, T4, T5, T6, T7, TRest&gt;
 
30ab651fcb4354552bd4891619a0bdd81e0ebdbf 
When using NuGet 3.x this package requires at least version 3.4.</dc:description>
  <dc:identifier>System.ValueTuple</dc:identifier>
  <version>4.5.0</version>
  <keywords></keywords>
  <lastModifiedBy>NuGet.Packaging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35;Microsoft Windows NT 10.0.14393.0;.NET Framework 4.5</lastModifiedBy>
</coreProperties>