<?xml version="1.0" encoding="utf-8"?>
<coreProperties xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.openxmlformats.org/package/2006/metadata/core-properties">
  <dc:creator>Microsoft</dc:creator>
  <dc:description>Provides the System.Runtime.CompilerServices.Unsafe class, which provides generic, low-level functionality for manipulating pointers.

Commonly Used Types:
System.Runtime.CompilerServices.Unsafe
 
When using NuGet 3.x this package requires at least version 3.4.</dc:description>
  <dc:identifier>System.Runtime.CompilerServices.Unsafe</dc:identifier>
  <version>4.6.0</version>
  <keywords></keywords>
  <lastModifiedBy>NuGet.Packaging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35;</lastModifiedBy>
</coreProperties>