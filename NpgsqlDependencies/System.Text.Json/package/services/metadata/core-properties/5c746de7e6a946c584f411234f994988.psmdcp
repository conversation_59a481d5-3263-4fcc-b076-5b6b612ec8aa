<?xml version="1.0" encoding="utf-8"?>
<coreProperties xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.openxmlformats.org/package/2006/metadata/core-properties">
  <dc:creator>Microsoft</dc:creator>
  <dc:description>Provides high-performance and low-allocating types that serialize objects to JavaScript Object Notation (JSON) text and deserialize JSON text to objects, with UTF-8 support built-in. Also provides types to read and write JSON text encoded as UTF-8, and to create an in-memory document object model (DOM), that is read-only, for random access of the JSON elements within a structured view of the data.

Commonly Used Types:
System.Text.Json.JsonSerializer
System.Text.Json.JsonDocument
System.Text.Json.JsonElement
System.Text.Json.Utf8JsonWriter
System.Text.Json.Utf8JsonReader
 
When using NuGet 3.x this package requires at least version 3.4.</dc:description>
  <dc:identifier>System.Text.Json</dc:identifier>
  <version>4.6.0</version>
  <keywords></keywords>
  <lastModifiedBy>NuGet.Packaging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35;</lastModifiedBy>
</coreProperties>