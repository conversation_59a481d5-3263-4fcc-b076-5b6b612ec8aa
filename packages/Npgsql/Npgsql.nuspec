﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Npgsql</id>
    <version>4.1.13</version>
    <authors><PERSON>,<PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON></authors>
    <license type="expression">PostgreSQL</license>
    <licenseUrl>https://licenses.nuget.org/PostgreSQL</licenseUrl>
    <icon>postgresql.png</icon>
    <projectUrl>https://www.npgsql.org/</projectUrl>
    <description>Npgsql is the open source .NET data provider for PostgreSQL.</description>
    <copyright>Copyright 2024 © The Npgsql Development Team</copyright>
    <tags>npgsql postgresql postgres ado ado.net database sql</tags>
    <repository type="git" url="https://github.com/npgsql/npgsql" commit="db69b1c964e532d30a18ec5e11d8a4491f4be661" />
    <dependencies>
      <group targetFramework=".NETFramework4.6.1">
        <dependency id="Microsoft.Bcl.AsyncInterfaces" version="1.1.0" exclude="Build,Analyzers" />
        <dependency id="System.Memory" version="4.5.3" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="4.6.0" exclude="Build,Analyzers" />
        <dependency id="System.Text.Json" version="4.6.0" exclude="Build,Analyzers" />
        <dependency id="System.Threading.Tasks.Extensions" version="4.5.3" exclude="Build,Analyzers" />
        <dependency id="System.ValueTuple" version="4.5.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETCoreApp3.0">
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="4.6.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.0">
        <dependency id="Microsoft.Bcl.AsyncInterfaces" version="1.1.0" exclude="Build,Analyzers" />
        <dependency id="System.Memory" version="4.5.3" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="4.6.0" exclude="Build,Analyzers" />
        <dependency id="System.Text.Json" version="4.6.0" exclude="Build,Analyzers" />
        <dependency id="System.Threading.Tasks.Extensions" version="4.5.3" exclude="Build,Analyzers" />
        <dependency id="System.ValueTuple" version="4.5.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.1">
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="4.6.0" exclude="Build,Analyzers" />
        <dependency id="System.Text.Json" version="4.6.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>