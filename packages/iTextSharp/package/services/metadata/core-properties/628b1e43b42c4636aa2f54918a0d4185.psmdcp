<?xml version="1.0" encoding="utf-8"?>
<coreProperties xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.openxmlformats.org/package/2006/metadata/core-properties">
  <dc:creator>iText Software</dc:creator>
  <dc:description>PLEASE NOTE: iTextSharp is EOL, and has been replaced by iText 7. Only security fixes will be added

We HIGHLY recommend customers use iText 7 for new projects, and to consider moving existing projects from iTextSharp to iText 7 to benefit from the many improvements such as:
- HTML to PDF (PDF/A) conversion
- PDF Redaction
- SVG support
- Better language support (Indic, Thai, Khmer, Arabic, Hebrew)
- PDF Debugging for your IDE
- Data Extraction
- Better continued support and bugfixes
- More modular, extensible handling of your document workflow
- Extra practical add-ons
- Encryption, hashing and digital signatures

iText is a PDF library that allows you to CREATE, ADAPT, INSPECT and MAINTAIN documents in the Portable Document Format (PDF), allowing you to add PDF functionality to your software projects with ease.  We even have documentation to help you get coding.

iText 7 is available under AGPL and Commercial license. iText 7 Community: https://www.nuget.org/packages/itext7/

iText 7 is a complete re-write of iText 5, allowing you to choose your adventure with add-ons, all based on a simple, modular code structure that is easy to use and well documented.

iText 7 includes pdfDebug, the first debugging tool that gives you a clear overview of your content streams and document structure as well as pdfCalligraph, allowing you to leverage advanced typography.

Several iText engineers are actively supporting the project on StackOverflow: https://stackoverflow.com/questions/tagged/itext</dc:description>
  <dc:identifier>iTextSharp</dc:identifier>
  <version>********</version>
  <keywords>itext itextsharp c# .net csharp pdf</keywords>
  <lastModifiedBy>NuGet, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35;Microsoft Windows NT 6.2.9200.0;.NET Framework 4.7.2</lastModifiedBy>
</coreProperties>