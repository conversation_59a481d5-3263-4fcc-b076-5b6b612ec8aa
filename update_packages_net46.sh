#!/bin/bash

# Script para descargar las versiones más recientes de las librerías compatibles con .NET Framework 4.6
# Este script descargará las siguientes librerías:
# 1. BouncyCastle 1.9.0
# 2. iTextSharp ********
# 3. Npgsql 6.0.10 (última versión compatible con .NET Framework 4.6)
# 4. Newtonsoft.Json 13.0.3

# Crear directorios
mkdir -p UpdatedLibraries_NET46
cd UpdatedLibraries_NET46

echo "Creando directorios para las librerías actualizadas..."
mkdir -p BouncyCastle
mkdir -p iTextSharp
mkdir -p Npgsql
mkdir -p NewtonsoftJson

# Descargar las versiones más recientes de las librerías
echo "Descargando las versiones más recientes de las librerías..."

# BouncyCastle
echo "Descargando BouncyCastle 1.9.0..."
curl -L -o BouncyCastle.zip https://www.nuget.org/api/v2/package/Portable.BouncyCastle/1.9.0
unzip -o BouncyCastle.zip -d BouncyCastle

# iTextSharp
echo "Descargando iTextSharp ********..."
curl -L -o iTextSharp.zip https://www.nuget.org/api/v2/package/iTextSharp/********
unzip -o iTextSharp.zip -d iTextSharp

# Npgsql
echo "Descargando Npgsql 6.0.10..."
curl -L -o Npgsql.zip https://www.nuget.org/api/v2/package/Npgsql/6.0.10
unzip -o Npgsql.zip -d Npgsql

# Newtonsoft.Json
echo "Descargando Newtonsoft.Json 13.0.3..."
curl -L -o NewtonsoftJson.zip https://www.nuget.org/api/v2/package/Newtonsoft.Json/13.0.3
unzip -o NewtonsoftJson.zip -d NewtonsoftJson

# Extraer los DLLs a una carpeta común
echo "Extrayendo los DLLs a una carpeta común..."
mkdir -p ../UpdatedDLLs_NET46

# Copiar BouncyCastle DLL
cp BouncyCastle/lib/net40/BouncyCastle.Crypto.dll ../UpdatedDLLs_NET46/

# Copiar iTextSharp DLL
cp iTextSharp/lib/itextsharp.dll ../UpdatedDLLs_NET46/

# Copiar Npgsql DLL (elegir la versión adecuada para .NET Framework 4.6)
cp Npgsql/lib/net6.0/Npgsql.dll ../UpdatedDLLs_NET46/

# Copiar Newtonsoft.Json DLL
cp NewtonsoftJson/lib/net45/Newtonsoft.Json.dll ../UpdatedDLLs_NET46/

echo "Descarga y extracción de paquetes completada."
echo "Los DLLs actualizados están disponibles en la carpeta UpdatedDLLs_NET46."
echo ""
echo "Próximos pasos:"
echo "1. Actualizar las referencias del proyecto para que apunten a estos nuevos DLLs"
echo "2. Probar exhaustivamente la aplicación después de las actualizaciones"
