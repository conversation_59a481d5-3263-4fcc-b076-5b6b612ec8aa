﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Configuration;
using System.Data.SqlClient;
using System.IO;
using Datos.Generales;
using Entidades.Conexion;
using Datos.Errores;

namespace Datos.Conexion
{
    public class ConfigurarDAL
    {
        public SqlConnection SQLConn = new SqlConnection();

        public bool grabarConfiguracion()
        {
            bool bContinuar = false; 
            string sServerSQL = string.Empty;

            if (obtenerServidor(ref sServerSQL))
            {
                bContinuar = obtenerDatosConexion(sServerSQL);
            }
            return bContinuar;
        }

        private bool obtenerServidor(ref string sServer)
        {
            string sRutaSysAdmon = "C:\\SYS\\PROGS\\sysadmon.dat";
            string sContenido = string.Empty;
            bool bContinuar = false;

            try
            {
                if (File.Exists(sRutaSysAdmon))
                {
                    using (var fs = new FileStream(sRutaSysAdmon, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                    using (var sr = new StreamReader(fs, Encoding.Default))
                    {
                        sContenido = sr.ReadLine();
                        var ips = sContenido.Trim().Split(' ').Where(elemento => elemento.Trim() != "");

                        sServer = probarConexion(ips.ToArray());
                    }

                    bContinuar = true;
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message.ToString());
            }
            return bContinuar;
        }

        private string probarConexion(string[] sIPs)
        {
            string sDB = "personal";
            string sUser = "sysaccesos";
            string sClave = "d894dab691238a6b66";
            string sConnString = string.Empty;
            string sServer = string.Empty;
            bool bContinuar = false;

            try
            {
                for (int i = 0; !bContinuar; i++)
                {
                    SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder();
                    builder.DataSource = sIPs[i];
                    builder.InitialCatalog = sDB;
                    builder.UserID = sUser;
                    builder.Password = sClave;

                    SQLConn.ConnectionString = builder.ConnectionString;
                    SQLConn.Open();
                    if (SQLConn.State == System.Data.ConnectionState.Open)
                    {
                        sServer = sIPs[i];
                        cerrarConexionSQL();
                        bContinuar = true;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message.ToString());
            }
            return sServer;
        }

        private bool obtenerDatosConexion(string sServer)
        {
            string sDB = "personal";
            string sUser = "sysaccesos";
            string sClave  = "d894dab691238a6b66";
            string rutaWS = "";
            //string sClaveCampoPass = "password";
            bool bContinuar = false; 

            SqlCommand cmd = new SqlCommand();
            SqlDataReader lector = null;
            GeneralesDAL general = new GeneralesDAL();

            try
            {
                SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder();
                builder.DataSource = sServer;
                builder.InitialCatalog = sDB;
                builder.UserID = sUser;
                builder.Password = sClave;
               
                SQLConn.ConnectionString = builder.ConnectionString;
                SQLConn.Open();

                if (SQLConn.State == System.Data.ConnectionState.Open)
                {
                    cmd.Connection = SQLConn;                 
                    cmd.CommandText = "SELECT clave, RTRIM(ip) AS ip, RTRIM(bd) AS bd, RTRIM(usuario) AS usuario, RTRIM(password) AS claveEncriptada FROM sapipsadmon WHERE clave IN(@clave1, @clave2, @clave3, @clave4) ORDER BY clave";
                   
                    //parámetros para la consulta
                    cmd.Parameters.AddWithValue("@clave1", 11);
                    cmd.Parameters.AddWithValue("@clave2", 31);
                    cmd.Parameters.AddWithValue("@clave3", 143);
                    cmd.Parameters.AddWithValue("@clave4", 192);

                    lector = cmd.ExecuteReader();

                    while (lector.Read())
                    {
                        if (int.Parse(lector["clave"].ToString()) == 11)
                        {
                            Entidades.Conexion.Conexion.sServidorPersonal = lector["ip"].ToString();
                            Entidades.Conexion.Conexion.sBDPersonal = lector["bd"].ToString();
                            Entidades.Conexion.Conexion.sUsuarioPersonal = lector["usuario"].ToString();
                            Entidades.Conexion.Conexion.sClavePersonal = lector["claveEncriptada"].ToString();
                            
                        }
                        else if (int.Parse(lector["clave"].ToString()) == 31)
                        {
                            Entidades.Conexion.Conexion.sServidorContabilidad = lector["ip"].ToString();
                            Entidades.Conexion.Conexion.sBDContabilidad = lector["bd"].ToString();
                            Entidades.Conexion.Conexion.sUsuarioContabilidad = lector["usuario"].ToString();
                            Entidades.Conexion.Conexion.sClaveContabilidad = lector["claveEncriptada"].ToString();
                        }
                        else if (int.Parse(lector["clave"].ToString()) == 143)
                        {
                            Entidades.Conexion.Conexion.sServidorAdministracion = lector["ip"].ToString();
                            Entidades.Conexion.Conexion.sBDAdministracion = lector["bd"].ToString();
                            Entidades.Conexion.Conexion.sUsuarioAdministracion = lector["usuario"].ToString();
                            Entidades.Conexion.Conexion.sClavedministracion = lector["claveEncriptada"].ToString();
                        }
                        else if (int.Parse(lector["clave"].ToString()) == 192)
                        {
                            Entidades.Conexion.Conexion.sServidorWebService = lector["ip"].ToString() + ":8080";
                            rutaWS = "http://{0}/ValidacionesCorreosElectronicos/WSValidacionCorreosElectronicos"; //?wsdl
                            rutaWS = string.Format(rutaWS, Entidades.Conexion.Conexion.sServidorWebService);
                            Entidades.Conexion.Conexion.sDescripcionWebService = rutaWS;
                            
                        }
                        bContinuar = true;                       
                    }
                   
                }
                cmd.Connection.Close();
                cerrarConexionSQL();
                lector.Close();                    
                SQLConn.Close();
                SQLConn.Dispose();
            }
            catch (Exception ex)
            {
               throw new Exception(ex.Message.ToString());              
            }
            finally
            {
                cerrarConexionSQL();
                lector.Close();                    
                SQLConn.Close();
                SQLConn.Dispose();
            }

            return bContinuar;
        }

        private void cerrarConexionSQL()
        {
            if (SQLConn.State == System.Data.ConnectionState.Open)
            {
                SQLConn.Close();
            }
        }
    }
}
