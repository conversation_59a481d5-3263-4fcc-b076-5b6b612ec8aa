﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Npgsql;
using NpgsqlTypes;
using Entidades.CorreoEmpleado;
using Datos.Errores;
using System.Data.SqlClient;

namespace Datos.Correo
{
    public class CatalogoCorreoDAL : Conexion.ConexionDAL
    {
        public List<CatalogoCorreo> consultarCorreos()
        {
            //SqlConnection connection = DB.GetConnectionSQLServer(DB.NAME.administracion, "10.44.15.182");            
            NpgsqlCommand cmd = new NpgsqlCommand();
            NpgsqlDataReader lector;
            cmd.Connection = nConexion;
            List<CatalogoCorreo> listaCorreos = new List<CatalogoCorreo>();

            try
            {
                cmd.CommandText = "SELECT inumeroempleado, cemailempleado FROM fun_obtienecorreoempleados();"; //Obtiene los correos de la BD Personal

                conectarBaseDatos(2);

                lector = cmd.ExecuteReader();

                while (lector.Read())
                {
                    CatalogoCorreo CorreoEmpleado = new CatalogoCorreo();
                    CorreoEmpleado.iNumEmpleado = int.Parse(lector["inumeroempleado"].ToString());
                    CorreoEmpleado.sCorreoEmpleado = lector["cemailempleado"].ToString();
                    CorreoEmpleado.dFechaCaptura = DateTime.Now.Date;
                    listaCorreos.Add(CorreoEmpleado);
                }
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Datos.Correo", "CatalogoCorreoDAL.cs", "consultarCorreos", "Error al obtener los correos de fun_obtienecorreoempleados()", 9, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
            finally
            {
                cerrarConexion();
            }

            return listaCorreos;
        }

        public int replicarCorreosEmpleados(int iNumeroEmpleado, string sCorreo, DateTime dFechaCaptura)
        {
            NpgsqlCommand cmd = new NpgsqlCommand();
            cmd.Connection = nConexion;
            NpgsqlDataReader lector;
            int iRegresa = 0; //1-Actualizo registro, 2.-Inserto Registro

            try
            {
                cmd.CommandText = "SELECT fun_almacenacorreosempleados AS iRegresa FROM fun_almacenacorreosempleados(@iNumeroEmpleado,@cCorreo,@dFechaCaptura)"; //Graba los correos en la BD nominacontabilidad
                cmd.Parameters.AddWithValue("@iNumeroEmpleado", NpgsqlDbType.Integer, iNumeroEmpleado);
                cmd.Parameters.AddWithValue("@cCorreo", NpgsqlDbType.Char, 25, sCorreo);
                cmd.Parameters.AddWithValue("@dFechaCaptura", NpgsqlDbType.Date, dFechaCaptura);

                conectarBaseDatos(3); //Administracion
                lector = cmd.ExecuteReader();

                if (lector.Read())
                {
                    iRegresa = Convert.ToInt16(lector["iRegresa"]);
                }
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Datos.Correo", "CatalogoCorreoDAL.cs", "replicarCorreosEmpleados", "Error al replicar correos fun_almacenacorreosempleados()", 10, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
            finally
            {
                cerrarConexion();
            }

            return iRegresa;
        }
    }
}
