﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Npgsql;
using NpgsqlTypes;

namespace Datos.Errores
{
    public class GrabarErrorDAL : Conexion.ConexionDAL
    {
        public bool grabarError(string sComponente, string sClase, string sMetodo, string sSQLTexto, int iSQLNumError, string sSQLError)
        {
            NpgsqlCommand cmd = new NpgsqlCommand();
           // NpgsqlDataReader lector;
            cmd.Connection = nConexion;
            bool bGrabo = false;
              
            try
            {
                cmd.CommandText = "SELECT * FROM fun_grabarerrorpdfnomina(@cComponente,@cClase,@cMetodo,@cSQLTexto,@iSQLNumError,@cSQLError);";
                cmd.Parameters.AddWithValue("@cComponente", NpgsqlDbType.Char,40, sComponente);
                cmd.Parameters.AddWithValue("@cClase", NpgsqlDbType.Char,40, sClase);
                cmd.Parameters.AddWithValue("@cMetodo", NpgsqlDbType.Char,40, sMetodo);
                cmd.Parameters.AddWithValue("@cSQLTexto", NpgsqlDbType.Char,1000, sSQLTexto);
                cmd.Parameters.AddWithValue("@iSQLNumError", NpgsqlDbType.Integer, iSQLNumError);
                cmd.Parameters.AddWithValue("@cSQLError", NpgsqlDbType.Char,1000, sSQLError);

                conectarBaseDatos(3); //Administracion
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message.ToString());
            }
            finally
            {
                cerrarConexion(); 
            }

            return bGrabo;
        }
    }
}
