﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Npgsql;
using NpgsqlTypes;
using Entidades.Control;

namespace Datos.Control
{
    public class NominaControlDAL : Conexion.ConexionDAL
    {
        public NominaControl obtenerFechaNomina()
        {
            NpgsqlCommand cmd = new NpgsqlCommand();
            NpgsqlDataReader lector;
            NominaControl nominaControl = new NominaControl();

            try
            {
                conectarBaseDatos(1); //Nomina Contabilidad
                cmd.CommandText = "SELECT dFechaNomina, dFechaReparto, dFechaAguinaldo, iGeneraUtilidades, iGeneraAguinaldo FROM fun_consultarfechanomina()";
                cmd.Connection = nConexion;

                lector = cmd.ExecuteReader();


                if (lector.Read())
                {
                    nominaControl.dFechaNomina = Convert.ToDateTime(lector["dFechaNomina"]);
                    nominaControl.dFechaUtilidades = Convert.ToDateTime(lector["dFechaReparto"]);
                    nominaControl.dFechaAguinaldo = Convert.ToDateTime(lector["dFechaAguinaldo"]);
                    nominaControl.iGeneraUtilidades = Convert.ToInt32(lector["iGeneraUtilidades"]);
                    nominaControl.iGeneraAguinaldo = Convert.ToInt32(lector["iGeneraAguinaldo"]);
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message.ToString());
            }
            finally
            {
                cerrarConexion();
            }
            return nominaControl;
        }
    }
}
