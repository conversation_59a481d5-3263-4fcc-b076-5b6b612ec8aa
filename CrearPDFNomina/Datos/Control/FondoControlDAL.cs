﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Npgsql;
using NpgsqlTypes;
using Entidades.Control;

namespace Datos.Control
{
    public class FondoControlDAL: Conexion.ConexionDAL   
    {
        NpgsqlCommand cmd = new NpgsqlCommand();
        NpgsqlDataReader lector;

        public FondoControl obtenerFechaFondo(int iMovimiento)
        {
            /*NpgsqlCommand cmd = new NpgsqlCommand();
            NpgsqlDataReader lector;*/
            //DateTime Date = new DateTime(2016, 9, 01);
            FondoControl fondoControl = new FondoControl(); 

            try
            {
                conectarBaseDatos(3); //Administracion
                cmd.CommandText = "SELECT fun_verifica_estados_cuenta_fondo FROM fun_verifica_estados_cuenta_fondo(@imovimiento)";
                cmd.Parameters.AddWithValue("@imovimiento", NpgsqlDbType.Integer, iMovimiento);
                cmd.Connection = nConexion;

                lector = cmd.ExecuteReader();
                
                if (lector.Read())
                {
                    fondoControl.dFechaCorteFondo = Convert.ToDateTime(lector["fun_verifica_estados_cuenta_fondo"]);
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message.ToString());
            }
            finally
            {
                cerrarConexion();
            }
            return fondoControl;
        }

        public int GuardarFechaControl(DateTime dFechaFondo, int iTipo)
        {
            /*NpgsqlCommand cmd = new NpgsqlCommand();
            NpgsqlDataReader lector;*/
            cmd.Connection = nConexion;
            int iRegresa = 0;

            try
            {
                cmd.CommandText = "INSERT INTO mov_estados_cuenta_fondo SELECT @iTipo, @dFechaFondo, NOW();";
                cmd.Parameters.AddWithValue("@dFechaFondo", NpgsqlDbType.Date, dFechaFondo);
                cmd.Parameters.AddWithValue("@iTipo", NpgsqlDbType.Integer, iTipo);
                conectarBaseDatos(3); //Administracion
                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message.ToString());
            }
            finally
            {
                cerrarConexion();
            }

            return iRegresa;
        }
    }
}
