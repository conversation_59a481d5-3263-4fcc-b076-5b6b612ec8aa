﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Npgsql;
using NpgsqlTypes;
using Entidades.Nomina;

namespace Datos.Nomina
{
    public class NominaDetalleDAL : Conexion.ConexionDAL
    {
        public List<NominaDetalleEmpleado> consultarNominaDetalle(int iNumEmp, DateTime dFechaNomina, int iEmpresa)
        {
            NpgsqlCommand cmd = new NpgsqlCommand();
            NpgsqlDataReader lector;
            cmd.Connection = nConexion;
            cmd.CommandTimeout = 24000;

            List<NominaDetalleEmpleado> listaNominaDetalle = new List<NominaDetalleEmpleado>();

            try
            {
                cmd.CommandText = "SELECT itipomovimiento,cdescripcionmovimiento,cpercepciondeduccion,mimporte FROM fun_consultanominadetalleempleado(@iNumEmp,@dFechaNomina,@iEmpresa);";
                cmd.Parameters.AddWithValue("@iNumEmp", NpgsqlDbType.Integer, iNumEmp);
                cmd.Parameters.AddWithValue("@dFechaNomina", NpgsqlDbType.Date, dFechaNomina);
                cmd.Parameters.AddWithValue("@iEmpresa", NpgsqlDbType.Integer, iEmpresa);

                conectarBaseDatos(3); //Administracion
                lector = cmd.ExecuteReader();

                while (lector.Read())
                {
                    NominaDetalleEmpleado NominaDetalle = new NominaDetalleEmpleado();
                    NominaDetalle.iTipoMovimiento = int.Parse(lector["itipomovimiento"].ToString());
                    NominaDetalle.sDescripcionMovimiento = lector["cdescripcionmovimiento"].ToString();
                    NominaDetalle.sPercepcionDeduccion = lector["cpercepciondeduccion"].ToString();
                    NominaDetalle.dImporte = lector["mimporte"].ToString();
                    //NominaDetalle.dImporte = Convert.ToDecimal(lector["mimporte"]);
                    listaNominaDetalle.Add(NominaDetalle);
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message.ToString());
            }
            finally
            {
                cerrarConexion();
            }

            return listaNominaDetalle;
        }
    }
}
