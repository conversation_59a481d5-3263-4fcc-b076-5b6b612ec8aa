﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Npgsql;
using NpgsqlTypes;
using Entidades.Gasolina;
using Datos.Errores;

namespace Datos.Gasolina
{
    public class GasolinaDetallesDAL : Conexion.ConexionDAL
    {
        public List<EstadoDetalleGasolina> consultarEdoGasolinaDetalle(int iNumEmp, DateTime dFechaCorte)
        {
            NpgsqlCommand cmd = new NpgsqlCommand();
            NpgsqlDataReader lector;
            cmd.Connection = nConexion;
            cmd.CommandTimeout = 4000;

            List<EstadoDetalleGasolina> listaGasolinaxDetalle = new List<EstadoDetalleGasolina>();

            try
            {
                cmd.CommandText = "SELECT nom_empleado, nom_apellidopaterno, nom_apellidomaterno,nombrecentro,fechacorte,unidad,valen,proveedor,nombreproveedor,factura,iniciales,fecha_vale,importe,factor,retencion,total FROM fun_obtiene_estado_cuenta_gasolina(@iNumEmp,@dFechaCorte)";
                cmd.Parameters.AddWithValue("@iNumEmp", NpgsqlDbType.Integer, iNumEmp);
                cmd.Parameters.AddWithValue("@dFechaCorte", NpgsqlDbType.Date, dFechaCorte);
                conectarBaseDatos(3); //Administracion
                lector = cmd.ExecuteReader();

                while (lector.Read())
                {
                    EstadoDetalleGasolina DetalleGasolina = new EstadoDetalleGasolina();
                    DetalleGasolina.sNombre = lector["nom_empleado"].ToString();
                    DetalleGasolina.sApellidoPaterno = lector["nom_apellidopaterno"].ToString();
                    DetalleGasolina.sApellidoMaterno = lector["nom_apellidomaterno"].ToString();
                    DetalleGasolina.sDescripcionCentro = lector["nombrecentro"].ToString();
                    DetalleGasolina.dFechaCorte = Convert.ToDateTime(lector["fechacorte"].ToString());
                    DetalleGasolina.iUnidad = int.Parse(lector["unidad"].ToString());
                    DetalleGasolina.iVale = Int64.Parse(lector["valen"].ToString());
                    DetalleGasolina.iProveedor = int.Parse(lector["proveedor"].ToString());
                    DetalleGasolina.sNombreProveedor = lector["nombreproveedor"].ToString();
                    DetalleGasolina.sFactura = lector["factura"].ToString();
                    DetalleGasolina.sCiudad = lector["iniciales"].ToString();
                    DetalleGasolina.dFechaVale = Convert.ToDateTime(lector["fecha_vale"].ToString());
                    DetalleGasolina.iImporte = Convert.ToDecimal(lector["importe"]) / 100;
                    DetalleGasolina.iFactor = Convert.ToDecimal(lector["factor"]) / 100;
                    DetalleGasolina.iRetencion = Convert.ToDecimal(lector["retencion"]) / 100;
                    DetalleGasolina.iTotal = Convert.ToDecimal(lector["total"]) / 100;
                    listaGasolinaxDetalle.Add(DetalleGasolina);
                }
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Datos.Gasolina", "GasolinaDetallesDAL.cs", "consultarEdoGasolinaDetalle", "Error al detalles del empleado", 13, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
            finally
            {
                cerrarConexion();
            }

            return listaGasolinaxDetalle;
        }
    }
}
