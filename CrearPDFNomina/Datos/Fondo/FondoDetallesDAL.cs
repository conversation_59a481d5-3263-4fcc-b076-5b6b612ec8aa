﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Npgsql;
using NpgsqlTypes;
using Entidades.Fondo;
using Datos.Errores;

namespace Datos.Fondo
{
    public class FondoDetallesDAL : Conexion.ConexionDAL
    {
        public List<EstadoDetalleFondo> consultarEdoFondoDetalle(int iNumEmp, DateTime dFechaFondo)
        {
            NpgsqlCommand cmd = new NpgsqlCommand();
            NpgsqlDataReader lector;
            cmd.Connection = nConexion;
            cmd.CommandTimeout = 4000;
            List<EstadoDetalleFondo> listaEdoFondoDetalle = new List<EstadoDetalleFondo>();

            try
            {
                //cmd.CommandText = "select clave, importe from elpestadoscuenta where numemp= @iNumEmp and fecha='@dFechaNomina' order by clave;"; //Descripcion Movimiento
                cmd.CommandText = "SELECT num_empleado, clv_movimiento, fec_movimiento, imp_movimiento, des_movimiento FROM fun_obtiene_estado_cuenta_fondo(@iNumEmp,@dFechaNomina)";
                cmd.Parameters.AddWithValue("@iNumEmp", NpgsqlDbType.Integer, iNumEmp);
                cmd.Parameters.AddWithValue("@dFechaNomina", NpgsqlDbType.Date, dFechaFondo);
                conectarBaseDatos(3); //Administracion
                lector = cmd.ExecuteReader();

                while (lector.Read())
                {
                    EstadoDetalleFondo FondoDetalle = new EstadoDetalleFondo();
                    FondoDetalle.iClave = int.Parse(lector["clv_movimiento"].ToString());
                    FondoDetalle.sDescripcion = lector["des_movimiento"].ToString();
                    //FondoDetalle.iImporte = int.Parse(lector["imp_movimiento"].ToString()) / 100;
                    FondoDetalle.iImporte = Convert.ToDecimal(lector["imp_movimiento"]) /100;
                    listaEdoFondoDetalle.Add(FondoDetalle);
                }
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Datos.Fondo", "FondoDetallesDAL.cs", "consultarEdoFondoDetalle", "Error al replicar al obtener los detalles del empleado", 11, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
            finally
            {
                cerrarConexion();
            }

            return listaEdoFondoDetalle;
        }
    }
}
