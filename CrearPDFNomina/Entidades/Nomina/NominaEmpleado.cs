﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Entidades.Nomina
{
    public class NominaEmpleado
    {
        #region variables
        private int _iNumEmp;
        private string _sApellidoPaterno;
        private string _sApellidoMaterno;
        private string _sNombre;
        private int _iCentro;
        private string _sDescCentro;
        private int _iNumeroCiudad;
        private string _sDescCiudad;
        private string _sNumAfiliacion;
        private string _sCURP;
        private string _sRFC;
        private DateTime _dFechaFinal;
        private DateTime _dFechaInicio;
        private string _sTarjetaBanco;
        private string _sDescRutaPago;
        private string _sTarjetaDespensa;
        private string _dTotalDespensa;
        private string  _dTotalAPagar;
        private string  _dTotalIngresos;
        private string  _dTotalEgresos;
        private int _iNumeroEmpresa;
        private string _sNombreEmpresa;
        private string _sCorreoEmpleado;
        private string _cnombreempleadocorregido;
        private string _crfcempleadocorregido;
        #endregion variables

        #region constructor
        public NominaEmpleado()
        {
            _iNumEmp = 0;
            _sApellidoPaterno = string.Empty;
            _sApellidoMaterno = string.Empty;
            _sNombre = string.Empty;
            _iCentro = 0;
            _sDescCentro = string.Empty;
            _iNumeroCiudad = 0;
            _sDescCiudad = string.Empty;
            _sNumAfiliacion = string.Empty;
            _sCURP = string.Empty;
            _sRFC = string.Empty;
            _dFechaFinal = Convert.ToDateTime("1900-01-01");
            _dFechaInicio = Convert.ToDateTime("1900-01-01");
            _sTarjetaBanco = string.Empty;
            _sDescRutaPago = string.Empty;
            _sTarjetaDespensa = string.Empty;
            _dTotalDespensa = string.Empty;
            _dTotalAPagar = string.Empty;
            _dTotalIngresos = string.Empty;
            _dTotalEgresos = string.Empty;
            _iNumeroEmpresa = 0;
            _sNombreEmpresa = string.Empty;
            _sCorreoEmpleado = string.Empty;
            _cnombreempleadocorregido = string.Empty;
            _crfcempleadocorregido = string.Empty;
        }
        #endregion constructor

        #region propiedades
        public int iNumEmp
        {
            get
            {
                return _iNumEmp;
            }
            set
            {
                _iNumEmp = value;
            }
        }

        public string sApellidoPaterno
        {
            get
            {
                return _sApellidoPaterno;
            }
            set
            {
                _sApellidoPaterno = value;
            }
        }

        public string sApellidoMaterno
        {
            get
            {
                return _sApellidoMaterno;
            }
            set
            {
                _sApellidoMaterno = value;
            }
        }

        public string sNombre
        {
            get
            {
                return _sNombre;
            }
            set
            {
                _sNombre = value;
            }
        }

        public int iCentro
        {
            get
            {
                return _iCentro;
            }
            set
            {
                _iCentro = value;
            }
        }

        public string sDescCentro
        {
            get
            {
                return _sDescCentro;
            }
            set
            {
                _sDescCentro = value;
            }
        }

        public int iNumeroCiudad
        {
            get
            {
                return _iNumeroCiudad;
            }
            set
            {
                _iNumeroCiudad = value;
            }
        }

        public string sDescCiudad
        {
            get
            {
                return _sDescCiudad;
            }
            set
            {
                _sDescCiudad = value;
            }
        }

        public string sNumAfiliacion
        {
            get
            {
                return _sNumAfiliacion;
            }
            set
            {
                _sNumAfiliacion = value;
            }
        }

        public string sCURP
        {
            get
            {
                return _sCURP;
            }
            set
            {
                _sCURP = value;
            }
        }

        public string sRFC
        {
            get
            {
                return _sRFC;
            }
            set
            {
                _sRFC = value;
            }
        }

        public DateTime dFechaFinal
        {
            get
            {
                return _dFechaFinal;
            }
            set
            {
                _dFechaFinal = value;
            }
        }

        public DateTime dFechaInicio
        {
            get
            {
                return _dFechaInicio;
            }
            set
            {
                _dFechaInicio = value;
            }
        }

        public string sTarjetaBanco
        {
            get
            {
                return _sTarjetaBanco;
            }
            set
            {
                _sTarjetaBanco = value;
            }
        }

        public string sDescRutaPago
        {
            get
            {
                return _sDescRutaPago;
            }
            set
            {
                _sDescRutaPago = value;
            }
        }

        public string sTarjetaDespensa
        {
            get
            {
                return _sTarjetaDespensa;
            }
            set
            {
                _sTarjetaDespensa = value;
            }
        }

        public string dTotalDespensa
        {
            get
            {
                return _dTotalDespensa;
            }
            set
            {
                _dTotalDespensa = value;
            }

        }
       
        public string  dTotalAPagar
        {
            get
            {
                return _dTotalAPagar;
            }
            set
            {
                _dTotalAPagar = value;
            }

        }

        public string  dTotalIngresos
        {
            get
            {
                return _dTotalIngresos;
            }
            set
            {
                _dTotalIngresos = value;
            }
        }

        public string  dTotalEgresos
        {
            get
            {
                return _dTotalEgresos;
            }
            set
            {
                _dTotalEgresos = value;
            }
        }

        public int iNumeroEmpresa
        {
            get
            {
                return _iNumeroEmpresa;
            }
            set
            {
                _iNumeroEmpresa = value;
            }
        }

        public string sNombreEmpresa
        {
            get
            {
                return _sNombreEmpresa;
            }
            set
            {
                _sNombreEmpresa = value;
            }
        }

        public string sCorreoEmpleado
        {
            get
            {
                return _sCorreoEmpleado;
            }
            set
            {
                _sCorreoEmpleado = value;
            }
        }
        public string cNombreEmpleadoCorregido
        {
            get
            {
                return _cnombreempleadocorregido;
            }
            set
            {
                _cnombreempleadocorregido = value;
            }
        }
        public string cRfcEmpleadoCorregido
        {
            get
            {
                return _crfcempleadocorregido;
            }
            set
            {
                _crfcempleadocorregido = value;
            }
        }
        #endregion propiedades
    }
}
