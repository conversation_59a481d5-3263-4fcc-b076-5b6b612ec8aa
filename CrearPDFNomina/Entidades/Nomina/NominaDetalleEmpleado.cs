﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Entidades.Nomina
{
    public class NominaDetalleEmpleado
    {
        private int _iTipoMovimiento;
        private string _sDescripcionMovimiento;
        private string _dImporte;
        private string _sPercepcionDeduccion;

        public NominaDetalleEmpleado()
        {
             _iTipoMovimiento = 0;
             _sDescripcionMovimiento = string.Empty;
             _dImporte = string.Empty;
             _sPercepcionDeduccion = string.Empty;
        }

        public int iTipoMovimiento
        {
            get
            {
                return _iTipoMovimiento;
            }
            set
            {
                _iTipoMovimiento = value;
            }
        }

        public string sDescripcionMovimiento
        {
            get
            {
                return _sDescripcionMovimiento;
            }
            set
            {
                _sDescripcionMovimiento = value;
            }
        }

        public string sPercepcionDeduccion
        {
            get
            {
                return _sPercepcionDeduccion;
            }
            set
            {
                _sPercepcionDeduccion = value;
            }
        }

        public string dImporte
        {
            get
            {
                return _dImporte;
            }
            set
            {
                _dImporte = value;
            }
        }
    }
}
