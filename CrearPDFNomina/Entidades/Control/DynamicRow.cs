﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Dynamic;
using System.Linq;
using System.Text;

namespace Entidades.Control
{
    public class DynamicRow : DynamicObject
    {
        DataRow row;
        public DynamicRow(DataRow row)
        {
            this.row = row;
        }
        public override bool TryGetMember(GetMemberBinder binder, out object result)
        {
            result = row[binder.Name];
            return true;
        }
        public override bool TrySetMember(SetMemberBinder binder, object value)
        {
            row[binder.Name] = value;
            return true;
        }
    }
}

