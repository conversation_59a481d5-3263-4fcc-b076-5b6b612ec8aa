-- Function: fun_generartalonesempleados(integer, integer, character, character, character, bytea, date, character, integer)

-- DROP FUNCTION fun_generartalonesempleados(integer, integer, character, character, character, bytea, date, character, integer);

CREATE OR REPLACE FUNCTION fun_generartalonesempleados(integer, integer, character, character, character, bytea, date, character, integer)
  RETURNS smallint AS
$BODY$
DECLARE
	--Autor: 96862629 - Cabanillas Urias <PERSON>
	--BD: Administracion PostgreSQL
	--FECHA: 08/08/2016
	--SERVIDOR PRUEBAS: ************
	--SERVIDOR PRODUCCION: **********
	--Descripcion: Se encarga de generar la informacion de los empleados para el envio de talones.

	--Autor: 93114338 - Perez Cast<PERSON>#eda Jovany
	--FECHA: 28/07/2017
	--DESCRIPCION: Se modifica para agregar el parametro empresa
	
	iTipo 		 	ALIAS FOR $1;
	iNumEmp 		ALIAS FOR $2;
	cApellidoPaterno 	ALIAS FOR $3; 
	cApellidoMaterno 	ALIAS FOR $4; 
	cNombre 		ALIAS FOR $5;
	bPDF			ALIAS FOR $6;
	dFechaNomina     	ALIAS FOR $7; --Ya sea para nomina, fondo o gasolina
	cCorreoEmpleado  	ALIAS FOR $8;
	iEmpresa		ALIAS FOR $9;
	iRegresa 		SMALLINT DEFAULT 0; --1.-Actualizo talon, 2.-Inserto talon
	dFecha DATE;
BEGIN
	IF EXISTS(SELECT nom_empleado FROM mov_talonesempleadosmail WHERE num_empleado = iNumEmp AND fec_nomina = dFechaNomina AND clv_tipo = iTipo AND clv_empresa = iEmpresa) THEN
		UPDATE mov_talonesempleadosmail SET fec_nomina = dFechaNomina, des_correo = cCorreoEmpleado, des_archivopdf = bPDF 
		WHERE num_empleado = iNumEmp 
		AND clv_tipo = iTipo
		AND clv_empresa = iEmpresa;
		
		iRegresa = 1;
	ELSE
		INSERT INTO mov_talonesempleadosmail(num_empleado, nom_empleado,fec_nomina,des_correo,des_archivopdf,clv_tipo,clv_empresa) 
		--VALUES (iNumEmp,cApellidoPaterno ||' '|| cApellidoMaterno || ' ' || cNombre,dFechaNomina,cCorreoEmpleado,bPDF,iTipo);
		VALUES (iNumEmp,initcap(cNombre),dFechaNomina,cCorreoEmpleado,bPDF,iTipo,iEmpresa);
		
		iRegresa = 2;
	END IF; 
	
	RETURN iRegresa;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER;
  
GRANT EXECUTE ON FUNCTION fun_generartalonesempleados(integer, integer, character, character, character, bytea, date, character, integer) TO sysgenexus;