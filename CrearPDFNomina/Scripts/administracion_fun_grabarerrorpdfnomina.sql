CREATE OR REPLACE FUNCTION fun_grabarerrorpdfnomina(character, character, character, character, integer, character)
  RETURNS void AS
$BODY$
  DECLARE
    cComponente ALIAS FOR $1; 
    cClase ALIAS FOR $2; 
    cMetodo ALIAS FOR $3; 
    cSQLTexto ALIAS FOR $4; 
    iSQLNumError ALIAS FOR $5; 
    cSQLError ALIAS FOR $6;

	--Autor: 96862629 - Cabanillas Urias Jaime <PERSON> 
	--BD: Administracion PostgreSQL
	--FECHA: 08/08/2016
	--SERVIDOR PRUEBAS: ************
	--SERVIDOR PRODUCCION: **********
	--Descripcion: Se encarga de agregar los errores del envio de pdfs.

  BEGIN 
	INSERT INTO sysmensajeerrorpdf 
	VALUES (cComponente,cClase,cMetodo,cSQLTexto,iSQLNumError,cSQLError,now());
  END; 
$BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION fun_grabarerrorpdfnomina(character, character, character, character, integer, character) TO sysgenexus;