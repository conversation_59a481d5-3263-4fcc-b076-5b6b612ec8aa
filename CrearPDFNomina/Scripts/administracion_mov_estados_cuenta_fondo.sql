/*
	NOMBRE: <PERSON><PERSON><PERSON>#eda 93114338
	BD: Administracion PostgreSQL
	FECHA: 29/07/2016
	SERVIDOR PRUEBAS: ************
	SERVIDOR PRODUCCION: **********
	DESCRIPCION: Tabla donde se almacenara el movimiento de generacion de envio de estados de cuenta de fondo.
	MODULO:
	RUTA:
*/

CREATE TABLE mov_estados_cuenta_fondo
(
	clv_movimiento INT NOT NULL DEFAULT 0,
	fec_estado_cuenta DATE NOT NULL DEFAULT NOW(),
	fec_generacion TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

GRANT ALL ON mov_estados_cuenta_fondo TO sysgenexus;

CREATE INDEX idx_mov_estados_cuenta_fondo
ON mov_estados_cuenta_fondo
USING btree
(clv_movimiento,fec_estado_cuenta);

INSERT INTO mov_estados_cuenta_fondo
SELECT 2, '20160730', NOW();