/*
	NOMBRE: <PERSON><PERSON><PERSON>#eda 93114338
	BD: Administracion PostgreSQL
	FECHA: 29/07/2016
	SERVIDOR PRUEBAS: ************
	SERVIDOR PRODUCCION: **********
	DESCRIPCION: Tabla que contendrá el catalogo de movimientos de los estados de cuenta de fondo
	MODULO:
	RUTA:
*/

CREATE TABLE cat_movimientos_estados_fondo
(
	clv_movimiento INT NOT NULL DEFAULT 0,
	des_movimiento VARCHAR(50) NOT NULL DEFAULT '',
	des_movimiento_fondo VARCHAR(100) NOT NULL DEFAULT '',
	clv_tipo INT NOT NULL DEFAULT 0,
	num_capturo INT NOT NULL DEFAULT 0,
	fec_captura DATE NOT NULL DEFAULT NOW()
);

GRANT ALL ON cat_movimientos_estados_fondo TO sysgenexus;

CREATE INDEX idx_cat_movimientos_estados_fondo
ON cat_movimientos_estados_fondo
USING btree
(clv_movimiento,clv_tipo);

COPY cat_movimientos_estados_fondo FROM '/tmp/cat_movimientos_estados_fondo.txt';

UPDATE cat_movimientos_estados_fondo SET des_movimiento = 'SALDO INIC' WHERE clv_movimiento = 100;
UPDATE cat_movimientos_estados_fondo SET des_movimiento = 'INICIAL' WHERE clv_movimiento IN (160,120,303,191,140,130,308,175,185);
UPDATE cat_movimientos_estados_fondo SET des_movimiento = '+ PRESTAMOS' WHERE clv_movimiento = 101;
UPDATE cat_movimientos_estados_fondo SET des_movimiento = '+ MUEBLES' WHERE clv_movimiento = 102;
UPDATE cat_movimientos_estados_fondo SET des_movimiento = '+ ROPA' WHERE clv_movimiento = 103;
UPDATE cat_movimientos_estados_fondo SET des_movimiento = '- CONTADOS' WHERE clv_movimiento = 104;
UPDATE cat_movimientos_estados_fondo SET des_movimiento = '- ABO NOMINA' WHERE clv_movimiento IN (105,161);
UPDATE cat_movimientos_estados_fondo SET des_movimiento = '- ABO DIRECT' WHERE clv_movimiento IN (106,162);
UPDATE cat_movimientos_estados_fondo SET des_movimiento = '+ INTERESES' WHERE clv_movimiento IN (107,164);
UPDATE cat_movimientos_estados_fondo SET des_movimiento = '= FINAL' WHERE clv_movimiento IN (109,170,128,304,198,148,138,309,180,190);
UPDATE cat_movimientos_estados_fondo SET des_movimiento = '+ APORTAC.' WHERE clv_movimiento IN (121,301,192,141,131,306,176,186);
UPDATE cat_movimientos_estados_fondo SET des_movimiento = '- RETIROS' WHERE clv_movimiento IN (122,305,193,142,132,310);
UPDATE cat_movimientos_estados_fondo SET des_movimiento = '+ INTERES' WHERE clv_movimiento IN (123,302,194,143,133,307,177,187);
UPDATE cat_movimientos_estados_fondo SET des_movimiento = 'MARGEN DE CREDITO' WHERE clv_movimiento = 150;
UPDATE cat_movimientos_estados_fondo SET des_movimiento = 'MARGEN DE COMPRA' WHERE clv_movimiento = 151;