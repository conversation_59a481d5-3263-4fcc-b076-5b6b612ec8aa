CREATE TABLE cat_pdfnomina_configuraciones(
	idu_configuracion SERIAL primary key,
	idu_empresa integer NOT NULL,
	des_ruta_logo text not null default '',
	num_ancho integer not null  default 0,
	num_alto integer not null default 0,
	des_autor text not null default '',
	opc_estatus bit(1) not null default B'1',
	fec_registro TIMESTAMP witHout TIME ZONE NOT NULL DEFAULT CURRENT_DATE::TIMESTAMP WITHOUT TIME ZONE,
	fec_actualizacion TIMESTAMP witHout TIME ZONE NOT NULL DEFAULT CURRENT_DATE::TIMESTAMP WITHOUT TIME ZONE,
	CONSTRAINT fk_pdfnomina_configuraciones_cat_empresas_gx FOREIGN KEY (idu_empresa) REFERENCES cat_empresas_gx (idu_empresa) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION
);

INSERT INTO cat_pdfnomina_configuraciones (idu_empresa, des_ruta_logo, num_ancho, num_alto, des_autor, opc_estatus, fec_registro) VALUES
(1,'C:\\sys\\progs\\logocoppel.jpg', 150, 60, 'COPPEL S.A. DE C.V.', B'1', CURRENT_DATE::TIMESTAMP),
(15,'C:\\sys\\progs\\logoaforecoppel.jpg', 180, 40, 'AFORE COPPEL S.A. DE C.V.', B'1', CURRENT_DATE::TIMESTAMP),
(16,'C:\\sys\\progs\\logobancoppel.jpg', 180, 35, 'BANCOPPEL S.A. INSTITUCIÓN DE BANCA MÚLTIPLE', B'1', CURRENT_DATE::TIMESTAMP),
(20,'C:\\sys\\progs\\logocreditoycasa.jpg', 150, 60, 'CREDITO Y CASA S.A.', B'1', CURRENT_DATE::TIMESTAMP),
(22,'C:\\sys\\progs\\logocoppel.jpg', 150, 60, 'VADUM S.A. DE C.V.', B'1', CURRENT_DATE::TIMESTAMP);