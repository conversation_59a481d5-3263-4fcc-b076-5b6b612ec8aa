﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Configuration;
using System.IO;
using iTextSharp.text;
using iTextSharp.text.pdf;
using Entidades.Fondo;
using System.Windows.Forms;
using System.Globalization;

namespace Negocios.PDF
{
    public class PDFFondoBO
    {
        public void construirPDFFondo(DateTime dFechaFondo, int iNumEmp, string sApellidoPaterno, string sApellidoMaterno, string sNombre, string sRfc, string sCurp,
            DateTime dFechaAlta, int iSueldoMensual, int iCentro, string sDescripcionCentro, int iNumeroSeguro, decimal iImporteSeguro, List<EstadoDetalleFondo> listaDetalleFondoEmpleado)
        {
            Document document = new Document(PageSize.LETTER.Rotate(),1,1,20,1); //Tamaño de Hoja LETTER = CARTA y horizontal
            var rectangulo = new Rectangle(document.PageSize);
            string sRutaCarpeta = @"C:\PDF\"; //Se obtiene la ruta donde se generara el PDF
            bool bExisteCarpeta = Directory.Exists(sRutaCarpeta);
            string sRutaCompleta = "" + sRutaCarpeta + "" + iNumEmp.ToString() + "_" + dFechaFondo.ToString("yyyy-MM-dd") + ".pdf";
            string sAutor = string.Empty;
            string sPaquetes = string.Empty;
            float[] anchoDeColumnas = new float[9] { 13f, 10f, 13f, 10f, 10f, 13f, 13f, 15f, 13f}; //Se genera variable para ancho de las columnas
            float[] anchocolumnatitulo = new float[6] { 10f, 50f, 7f, 30f, 7f, 18f };
            float[] anchomargenes = new float[4] { 20f, 30f, 20f, 30f };
            float[] anchofaer = new float[5] { 13f, 13f, 35f, 30f, 30f };

            //iNumeroSeguro = 1; //QUITAR

            if (listaDetalleFondoEmpleado[30].iImporte == 0) //autocop
            {
                anchoDeColumnas[2] = 10f;
                anchoDeColumnas[3] = 0f;
            }
            
            if (listaDetalleFondoEmpleado[39].iImporte == 0) //ahorro adicional II
            {
                anchoDeColumnas[8] = 2f;
            }
            if (listaDetalleFondoEmpleado[34].iImporte == 0) //Faer
            {
                anchofaer[0] = 0f;
                anchofaer[1] = 0f;
                anchofaer[2] = 0f;
                anchofaer[3] = 0f;
                anchofaer[4] = 0f;
            }
            
            iTextSharp.text.Font fontTitle = FontFactory.GetFont("Arial", 8);
            iTextSharp.text.Font fontText = FontFactory.GetFont("Arial", 7);

            if (bExisteCarpeta == false)
            {
                Directory.CreateDirectory(sRutaCarpeta);
            }

            PdfWriter.GetInstance(document, new FileStream(sRutaCompleta, FileMode.Create));

            //Configuracion del PDF 
            document.AddTitle("Fondo de Retiro");
            document.AddAuthor(sAutor);
            document.Open();


            Paragraph paragraph = new Paragraph();
            paragraph.Clear();
            paragraph.Alignment = Element.ALIGN_LEFT;
            paragraph.SetLeading(0.0f, 0.2f);
            paragraph.Font = fontText;
            paragraph.Add("\n");
            document.Add(paragraph);

            //Generacion del Detalle Titulos
            PdfPTable TablaTitulos = new PdfPTable(6);
            TablaTitulos.SetWidths(anchocolumnatitulo);
            PdfPTable tablaDetalleEdoFondo = new PdfPTable(9);
            tablaDetalleEdoFondo.SetWidths(anchoDeColumnas);
            PdfPTable tablaMargenes = new PdfPTable(4);
            tablaMargenes.SetWidths(anchomargenes);
            PdfPTable tablafaer = new PdfPTable(5);
            tablafaer.SetWidths(anchofaer);

            CultureInfo cinfo = new CultureInfo("es-MX");
            string sfechaConf = dFechaFondo.ToString("dd/MMMM/yyyy").ToUpper();

            paragraph.Clear();
            paragraph.Alignment = Element.ALIGN_CENTER;
            paragraph.Font = fontTitle;
            paragraph.Add("FONDO DE RETIRO - ESTADO DE CUENTA MENSUAL - AL " +  sfechaConf);
            document.Add(paragraph);

            paragraph.Clear();
            paragraph.Add("\n\n");
            document.Add(paragraph);
            paragraph.Clear();
            paragraph.Add("\n\n");
            document.Add(paragraph);

            //CONFIRGURACION DE CELDAS
            ConfigurarCeldasPDF ConfCelda = new ConfigurarCeldasPDF();

            //Datos del colaborador
            PdfPCell CeldaNumEmp = new PdfPCell(new Phrase(String.Format("{0:G}", iNumEmp), fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaNumEmp, 0, 0,  0, 0, 0, 0, 1);
            TablaTitulos.AddCell(CeldaNumEmp);

            PdfPCell CeldaNombre = new PdfPCell(new Phrase(String.Format("{0:G}", sApellidoPaterno + " " + sApellidoMaterno + " " + sNombre), fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaNombre, 0, 0,  0, 0, 0, 0, 1);
            TablaTitulos.AddCell(CeldaNombre);
            

            PdfPCell CeldaTituloRFC = new PdfPCell(new Phrase("RFC: ", fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaTituloRFC,  0, 0, 0, 0, 0, 0, 1);
            TablaTitulos.AddCell(CeldaTituloRFC);

            PdfPCell CeldaRFC = new PdfPCell(new Phrase(String.Format("{0:G}", sRfc), fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaRFC,  0, 0, 0, 0, 0, 0, 1);
            TablaTitulos.AddCell(CeldaRFC);

            PdfPCell CeldaTituloCurp = new PdfPCell(new Phrase("CURP: ", fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaTituloCurp,  0, 0, 0, 0, 0, 0, 1);
            TablaTitulos.AddCell(CeldaTituloCurp);

            PdfPCell CeldaCurp = new PdfPCell(new Phrase(String.Format("{0:G}", sCurp), fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaCurp,  0, 0, 0, 0, 0, 0, 1);
            TablaTitulos.AddCell(CeldaCurp);

            //Next 
            PdfPCell CeldaFecha = new PdfPCell(new Phrase(String.Format("{0:G}", dFechaAlta.ToString("yyyy-MM-dd")), fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaFecha,  0, 0, 0, 0, 0, 0, 1);
            TablaTitulos.AddCell(CeldaFecha);

            PdfPCell CeldaSueldo = new PdfPCell(new Phrase(String.Format("{0:N}", iSueldoMensual / 100), fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaSueldo,  0, 0, 0, 0, 0, 0, 1);
            TablaTitulos.AddCell(CeldaSueldo);

            PdfPCell CeldanumCentro = new PdfPCell(new Phrase(String.Format("{0:G}", iCentro), fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldanumCentro,  0, 0, 0, 0, 0, 0, 1);
            TablaTitulos.AddCell(CeldanumCentro);

            PdfPCell CeldaCentro = new PdfPCell(new Phrase(String.Format("{0:G}", sDescripcionCentro), fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaCentro,  0, 0, 0, 0, 0, 0, 1);
            TablaTitulos.AddCell(CeldaCentro);

            PdfPCell nulo = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontTitle));
            ConfCelda.PropiedadesCeldadas(nulo, 0, 0, 0, 0, 0, 0, 0);
            TablaTitulos.AddCell(nulo);

            PdfPCell nulo2 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontTitle));
            ConfCelda.PropiedadesCeldadas(nulo2, 0, 0, 0, 0, 0, 0, 0);
            TablaTitulos.AddCell(nulo2);

            document.Add(TablaTitulos); //Finaliza el detalle de la tabla de titulos

            paragraph.Clear();
            paragraph.Add("\n\n");
            document.Add(paragraph);
            paragraph.Clear();
            paragraph.Add("\n\n");
            document.Add(paragraph);

            //Titulos de Celdas
            PdfPCell CeldaTituloCtaCorriente = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(CeldaTituloCtaCorriente, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(CeldaTituloCtaCorriente);

            PdfPCell CeldaCtaCorriente = new PdfPCell(new Phrase("CTA CORR", fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaCtaCorriente, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(CeldaCtaCorriente);

            if (listaDetalleFondoEmpleado[30].iImporte != 0) //movimiento 170
            {
                PdfPCell CeldaTituloAutocop = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(CeldaTituloAutocop, 0, 0, 0, 0, 0, 0, 0);
                tablaDetalleEdoFondo.AddCell(CeldaTituloAutocop); 

                PdfPCell CeldaAutoCop = new PdfPCell(new Phrase("AUTOCOP", fontTitle));
                ConfCelda.PropiedadesCeldadas(CeldaAutoCop, 0, 0, 0, 0, 0, 0, 2);
                tablaDetalleEdoFondo.AddCell(CeldaAutoCop);
            }
            else
            {
                PdfPCell cell = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell, 0, 0, 0, 0, 0, 0, 0);
                tablaDetalleEdoFondo.AddCell(cell);

                PdfPCell cell2 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell2, 0, 0, 0, 0, 0, 0, 0);
                tablaDetalleEdoFondo.AddCell(cell2);
            }

            PdfPCell CeldaTituloFdoEmpresa = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(CeldaTituloFdoEmpresa, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(CeldaTituloFdoEmpresa);

            PdfPCell CeldaFdoEmpresa = new PdfPCell(new Phrase("FONDO EMPRESA", fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaFdoEmpresa, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(CeldaFdoEmpresa);

            PdfPCell CeldaFdoTrabajador = new PdfPCell(new Phrase("FONDO TRABAJADOR", fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaFdoTrabajador, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(CeldaFdoTrabajador);

            PdfPCell CeldaAhoAdic = new PdfPCell(new Phrase("AHORRO ADIC.", fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaAhoAdic, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(CeldaAhoAdic);

            if (listaDetalleFondoEmpleado[39].iImporte != 0) //movimiento 191
            {
                PdfPCell CeldaAhoAdicII = new PdfPCell(new Phrase("AHORRO ADIC. II", fontTitle));
                ConfCelda.PropiedadesCeldadas(CeldaAhoAdicII, 0, 0, 0, 0, 0, 0, 2);
                tablaDetalleEdoFondo.AddCell(CeldaAhoAdicII);
            }
            else
            {
                PdfPCell cell = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell, 0, 0, 0, 0, 0, 0, 0);
                tablaDetalleEdoFondo.AddCell(cell);
            }

            //1er Renglon "INICIAL"
            //Titulos de Celdas
            PdfPCell Cell1 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[0].sDescripcion), fontText)); //Sueldo inicial
            ConfCelda.PropiedadesCeldadas(Cell1,  0, 0, 0, 0, 0, 0, 1);
            tablaDetalleEdoFondo.AddCell(Cell1);

            PdfPCell Cell2 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[0].iImporte), fontText)); //Sueldo inicial
            ConfCelda.PropiedadesCeldadas(Cell2, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(Cell2);

            if (listaDetalleFondoEmpleado[30].iImporte != 0) //movimiento 170 autocop
            {
                PdfPCell Cell3 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[26].sDescripcion), fontText)); //inicial
                ConfCelda.PropiedadesCeldadas(Cell3,  0, 0, 0, 0, 8, 0, 1);
                tablaDetalleEdoFondo.AddCell(Cell3);

                PdfPCell Cell4 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[26].iImporte), fontText)); //inicial
                ConfCelda.PropiedadesCeldadas(Cell4, 0, 0, 0, 0, 0, 0, 2);
                tablaDetalleEdoFondo.AddCell(Cell4);
            }
            else
            {
                PdfPCell cell = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell, 0, 0, 0, 0, 0, 0, 0);
                tablaDetalleEdoFondo.AddCell(cell);

                PdfPCell cell2 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell2, 0, 0, 0, 0, 0, 0, 0);
                tablaDetalleEdoFondo.AddCell(cell2);
            }

            PdfPCell cell5 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[9].sDescripcion), fontText)); //Fondo Empresa inicial
            ConfCelda.PropiedadesCeldadas(cell5,  0, 0, 0, 0, 8, 0, 1);
            tablaDetalleEdoFondo.AddCell(cell5);

            PdfPCell cell5total = new PdfPCell(new Phrase(String.Format("{0:N}", Convert.ToDecimal(listaDetalleFondoEmpleado[9].iImporte) + Convert.ToDecimal(listaDetalleFondoEmpleado[46].iImporte)), fontText)); //Fondo Empresa
            ConfCelda.PropiedadesCeldadas(cell5total, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(cell5total);

            PdfPCell cell5totaltrabajador = new PdfPCell(new Phrase(String.Format("{0:N}", Convert.ToDecimal(listaDetalleFondoEmpleado[14].iImporte) + Convert.ToDecimal(listaDetalleFondoEmpleado[51].iImporte)), fontText)); //Fondo Empresa
            ConfCelda.PropiedadesCeldadas(cell5totaltrabajador, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(cell5totaltrabajador);

            PdfPCell cell6 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[19].iImporte), fontText)); //Ahorro Adicional
            ConfCelda.PropiedadesCeldadas(cell6, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(cell6);

            if (listaDetalleFondoEmpleado[39].iImporte != 0) //movimiento 191 adicional II 
            {
                PdfPCell cell7 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[39].iImporte), fontText));
                ConfCelda.PropiedadesCeldadas(cell7, 0, 0, 0, 0, 0, 0, 2);
                tablaDetalleEdoFondo.AddCell(cell7);
            }
            else
            {
                PdfPCell cell = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell, 0, 0, 0, 0, 0, 0, 0);
                tablaDetalleEdoFondo.AddCell(cell);
            }

            //2do Renglon "APORTACIONES"
            //Titulos de Celdas
            PdfPCell Cell12 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[1].sDescripcion), fontText)); //Prestamos
            ConfCelda.PropiedadesCeldadas(Cell12,  0, 0, 0, 0, 0, 0, 1);
            tablaDetalleEdoFondo.AddCell(Cell12);

            PdfPCell Cell22 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[1].iImporte), fontText)); //Prestamos
            ConfCelda.PropiedadesCeldadas(Cell22, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(Cell22);

            if (listaDetalleFondoEmpleado[30].iImporte != 0) //movimiento 170 Autocop
            {
                PdfPCell Cell3 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[27].sDescripcion), fontText)); //Abono
                ConfCelda.PropiedadesCeldadas(Cell3,  0, 0, 0, 0, 8, 0, 1);
                tablaDetalleEdoFondo.AddCell(Cell3);

                PdfPCell Cell4 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[27].iImporte), fontText)); //Abono
                ConfCelda.PropiedadesCeldadas(Cell4, 0, 0, 0, 0, 0, 0, 2);
                tablaDetalleEdoFondo.AddCell(Cell4);
            }
            else
            {
                PdfPCell cell = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell, 0, 0, 0, 0, 0, 0, 0);
                tablaDetalleEdoFondo.AddCell(cell);

                PdfPCell cell2 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell2, 0, 0, 0, 0, 0, 0, 0);
                tablaDetalleEdoFondo.AddCell(cell2);
            }

            PdfPCell cell52 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[10].sDescripcion), fontText));
            ConfCelda.PropiedadesCeldadas(cell52,  0, 0, 0, 0, 8, 0, 1);
            tablaDetalleEdoFondo.AddCell(cell52);

            PdfPCell cell52total = new PdfPCell(new Phrase(String.Format("{0:N}", Convert.ToDecimal(listaDetalleFondoEmpleado[10].iImporte) + Convert.ToDecimal(listaDetalleFondoEmpleado[44].iImporte)), fontText));
            ConfCelda.PropiedadesCeldadas(cell52total, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(cell52total);

            PdfPCell cell52totaltrabajador = new PdfPCell(new Phrase(String.Format("{0:N}", Convert.ToDecimal(listaDetalleFondoEmpleado[15].iImporte) + Convert.ToDecimal(listaDetalleFondoEmpleado[49].iImporte)), fontText));
            ConfCelda.PropiedadesCeldadas(cell52totaltrabajador, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(cell52totaltrabajador);

            PdfPCell cell62 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[20].iImporte), fontText));
            ConfCelda.PropiedadesCeldadas(cell62, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(cell62);

            if (listaDetalleFondoEmpleado[39].iImporte != 0) //movimiento 191 adicional II
            {
                PdfPCell cell7 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[40].iImporte), fontText));
                ConfCelda.PropiedadesCeldadas(cell7, 0, 0, 0, 0, 0, 0, 2);
                tablaDetalleEdoFondo.AddCell(cell7);
            }
            else
            {
                PdfPCell cell = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell, 0, 0, 0, 0, 0, 0, 0);
                tablaDetalleEdoFondo.AddCell(cell);
            }

            //3er RENGLON "ABON DIRECTO"
            //Titulos de Celdas
            PdfPCell Cell13 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[2].sDescripcion), fontText)); //Muebles
            ConfCelda.PropiedadesCeldadas(Cell13,  0, 0, 0, 0, 0, 0, 1);
            tablaDetalleEdoFondo.AddCell(Cell13);

            PdfPCell Cell23 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[2].iImporte), fontText)); //Muebles
            ConfCelda.PropiedadesCeldadas(Cell23, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(Cell23);

            if (listaDetalleFondoEmpleado[30].iImporte != 0) //movimiento 170
            {
                PdfPCell Cell3 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[28].sDescripcion), fontText));
                ConfCelda.PropiedadesCeldadas(Cell3,  0, 0, 0, 0, 8, 0, 1);
                tablaDetalleEdoFondo.AddCell(Cell3);

                PdfPCell Cell4 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[28].iImporte), fontText));
                ConfCelda.PropiedadesCeldadas(Cell4, 0, 0, 0, 0, 0, 0, 2);
                tablaDetalleEdoFondo.AddCell(Cell4);
            }
            else
            {
                PdfPCell cell = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell, 0, 0, 0, 0, 0, 0, 0);
                tablaDetalleEdoFondo.AddCell(cell);

                PdfPCell cell2 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell2, 0, 0, 0, 0, 0, 0, 0);
                tablaDetalleEdoFondo.AddCell(cell2);
            }

            PdfPCell cell53 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[11].sDescripcion), fontText));
            ConfCelda.PropiedadesCeldadas(cell53,  0, 0, 0, 0, 8, 0, 1);
            tablaDetalleEdoFondo.AddCell(cell53);

            PdfPCell cell53total = new PdfPCell(new Phrase(String.Format("{0:N}", Convert.ToDecimal(listaDetalleFondoEmpleado[11].iImporte) + Convert.ToDecimal(listaDetalleFondoEmpleado[48].iImporte)), fontText));
            ConfCelda.PropiedadesCeldadas(cell53total, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(cell53total);

            PdfPCell cell53totaltrabajador = new PdfPCell(new Phrase(String.Format("{0:N}", Convert.ToDecimal(listaDetalleFondoEmpleado[16].iImporte) + Convert.ToDecimal(listaDetalleFondoEmpleado[53].iImporte)), fontText));
            ConfCelda.PropiedadesCeldadas(cell53totaltrabajador, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(cell53totaltrabajador);

            PdfPCell cell63 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[21].iImporte), fontText));
            ConfCelda.PropiedadesCeldadas(cell63, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(cell63);

            if (listaDetalleFondoEmpleado[39].iImporte != 0) //movimiento 191 adicional II
            {
                PdfPCell cell7 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[41].iImporte), fontText));
                ConfCelda.PropiedadesCeldadas(cell7, 0, 0, 0, 0, 0, 0, 2);
                tablaDetalleEdoFondo.AddCell(cell7);
            }
            else
            {
                PdfPCell cell = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell, 0, 0, 0, 0, 0, 0, 0);
                tablaDetalleEdoFondo.AddCell(cell);
            }

            //4to RENGLON "INTERESES"
            //Titulos de Celdas
            PdfPCell Cell14 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[3].sDescripcion), fontText)); //ROPA
            ConfCelda.PropiedadesCeldadas(Cell14,  0, 0, 0, 0, 0, 0, 1);
            tablaDetalleEdoFondo.AddCell(Cell14);

            PdfPCell Cell24 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[3].iImporte), fontText)); //ROPA
            ConfCelda.PropiedadesCeldadas(Cell24, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(Cell24);

            if (listaDetalleFondoEmpleado[30].iImporte != 0) //movimiento 170
            {
                PdfPCell Cell3 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[29].sDescripcion), fontText));
                ConfCelda.PropiedadesCeldadas(Cell3,  0, 0, 0, 0, 8, 0, 1);
                tablaDetalleEdoFondo.AddCell(Cell3);

                PdfPCell Cell4 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[29].iImporte), fontText));
                ConfCelda.PropiedadesCeldadas(Cell4, 0, 0, 0, 0, 0, 0, 2);
                tablaDetalleEdoFondo.AddCell(Cell4);
            }
            else
            {
                PdfPCell cell = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell, 0, 0, 0, 0, 0, 0, 0);
                tablaDetalleEdoFondo.AddCell(cell);

                PdfPCell cell2 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell2, 0, 0, 0, 0, 0, 0, 0);
                tablaDetalleEdoFondo.AddCell(cell2);
            }

            PdfPCell cell54 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[12].sDescripcion), fontText));
            ConfCelda.PropiedadesCeldadas(cell54,  0, 0, 0, 0, 8, 0, 1);
            tablaDetalleEdoFondo.AddCell(cell54);

            PdfPCell cell54total = new PdfPCell(new Phrase(String.Format("{0:N}", Convert.ToDecimal(listaDetalleFondoEmpleado[12].iImporte) + Convert.ToDecimal(listaDetalleFondoEmpleado[45].iImporte)), fontText));
            ConfCelda.PropiedadesCeldadas(cell54total, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(cell54total);

            PdfPCell cell54totaltrabajador = new PdfPCell(new Phrase(String.Format("{0:N}", Convert.ToDecimal(listaDetalleFondoEmpleado[17].iImporte) + Convert.ToDecimal(listaDetalleFondoEmpleado[50].iImporte)), fontText));
            ConfCelda.PropiedadesCeldadas(cell54totaltrabajador, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(cell54totaltrabajador);

            PdfPCell cell64 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[22].iImporte), fontText));
            ConfCelda.PropiedadesCeldadas(cell64, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(cell64);

            if (listaDetalleFondoEmpleado[39].iImporte != 0) //movimiento 191 adicional II
            {
                PdfPCell cell7 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[42].iImporte), fontText));
                ConfCelda.PropiedadesCeldadas(cell7, 0, 0, 0, 0, 0, 0, 2);
                tablaDetalleEdoFondo.AddCell(cell7);
            }
            else
            {
                PdfPCell cell = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell, 0, 0, 0, 0, 0, 0, 0);
                tablaDetalleEdoFondo.AddCell(cell);
            }

            //5to RENGLON "FINAL"
            //Titulos de Celdas
            PdfPCell Cell15 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[4].sDescripcion), fontText)); //CONTADOS
            ConfCelda.PropiedadesCeldadas(Cell15,  0, 0, 0, 0, 0, 0, 1);
            tablaDetalleEdoFondo.AddCell(Cell15);

            PdfPCell Cell25 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[4].iImporte), fontText)); //CONTADOS
            ConfCelda.PropiedadesCeldadas(Cell25, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(Cell25);

            if (listaDetalleFondoEmpleado[30].iImporte != 0) //movimiento 170
            {
                PdfPCell Cell3 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[30].sDescripcion), fontText));
                ConfCelda.PropiedadesCeldadas(Cell3,  0, 0, 0, 0, 8, 0, 1);
                tablaDetalleEdoFondo.AddCell(Cell3);

                PdfPCell Cell4 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[30].iImporte), fontText));
                ConfCelda.PropiedadesCeldadas(Cell4, 0, 0, 0, 0, 0, 0, 2);
                tablaDetalleEdoFondo.AddCell(Cell4);
            }
            else
            {
                PdfPCell cell = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell, 0, 0, 0, 0, 0, 0, 0);
                tablaDetalleEdoFondo.AddCell(cell);

                PdfPCell cell2 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell2, 0, 0, 0, 0, 0, 0, 0);
                tablaDetalleEdoFondo.AddCell(cell2);
            }

            PdfPCell cell55 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[13].sDescripcion), fontText));
            ConfCelda.PropiedadesCeldadas(cell55,  0, 0, 0, 0, 8, 0, 1);
            tablaDetalleEdoFondo.AddCell(cell55);

            PdfPCell cell55total = new PdfPCell(new Phrase(String.Format("{0:N}", Convert.ToDecimal(listaDetalleFondoEmpleado[13].iImporte) + Convert.ToDecimal(listaDetalleFondoEmpleado[47].iImporte)), fontText));
            ConfCelda.PropiedadesCeldadas(cell55total, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(cell55total);

            PdfPCell cell55totaltrabajador = new PdfPCell(new Phrase(String.Format("{0:N}", Convert.ToDecimal(listaDetalleFondoEmpleado[18].iImporte) + Convert.ToDecimal(listaDetalleFondoEmpleado[52].iImporte)), fontText));
            ConfCelda.PropiedadesCeldadas(cell55totaltrabajador, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(cell55totaltrabajador);

            PdfPCell cell65 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[23].iImporte), fontText));
            ConfCelda.PropiedadesCeldadas(cell65, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(cell65);

            if (listaDetalleFondoEmpleado[39].iImporte != 0) //movimiento 191 adicional II
            {
                PdfPCell cell7 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[43].iImporte), fontText));
                ConfCelda.PropiedadesCeldadas(cell7, 0, 0, 0, 0, 0, 0, 2);
                tablaDetalleEdoFondo.AddCell(cell7);
            }
            else
            {
                PdfPCell cell = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell, 0, 0, 0, 0, 0, 0, 0);
                tablaDetalleEdoFondo.AddCell(cell);
            }

            //6to RENGLON "abono nomina"
            //Titulos de Celdas
            PdfPCell Cell16 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[5].sDescripcion), fontText)); //abono nomina
            ConfCelda.PropiedadesCeldadas(Cell16,  0, 0, 0, 0, 0, 0, 1);
            tablaDetalleEdoFondo.AddCell(Cell16);

            PdfPCell Cell26 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[5].iImporte), fontText)); //abono nomina
            ConfCelda.PropiedadesCeldadas(Cell26, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(Cell26);

            PdfPCell cellnull1 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull1, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull1);

            PdfPCell cellnull2 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull2, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull2);

            PdfPCell cellnull3 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull3, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull3);

            PdfPCell cellnull4 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull4, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull4);

            PdfPCell cellnull5 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull5, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull5);

            PdfPCell cellnull6 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull6, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull6);

            PdfPCell cellnull7 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull7, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull7);


            //7to RENGLON "abono directo"
            //Titulos de Celdas
            PdfPCell Cell17 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[6].sDescripcion), fontText)); //abono nomina
            ConfCelda.PropiedadesCeldadas(Cell17,  0, 0, 0, 0, 0, 0, 1);
            tablaDetalleEdoFondo.AddCell(Cell17);

            PdfPCell Cell27 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[6].iImporte), fontText)); //abono nomina
            ConfCelda.PropiedadesCeldadas(Cell27, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(Cell27);

            PdfPCell cellnull17 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull17, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull17);

            PdfPCell cellnull27 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull27, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull27);

            PdfPCell cellnull37 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull37, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull37);

            PdfPCell cellnull47 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull47, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull47);

            PdfPCell cellnull57 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull57, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull57);

            PdfPCell cellnull67 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull67, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull67);

            PdfPCell cellnull77 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull77, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull77);

            //8to RENGLON "intereses"
            //Titulos de Celdas
            PdfPCell Cell18 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[7].sDescripcion), fontText)); //abono nomina
            ConfCelda.PropiedadesCeldadas(Cell18,  0, 0, 0, 0, 0, 0, 1);
            tablaDetalleEdoFondo.AddCell(Cell18);

            PdfPCell Cell28 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[7].iImporte), fontText)); //abono nomina
            ConfCelda.PropiedadesCeldadas(Cell28, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(Cell28);

            PdfPCell cellnull18 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull18, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull18);

            PdfPCell cellnull28= new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull28, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull28);

            PdfPCell cellnull38 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull38, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull38);

            PdfPCell cellnull48 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull48, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull48);

            PdfPCell cellnull58 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull58, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull58);

            PdfPCell cellnull68 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull68, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull68);

            PdfPCell cellnull78 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull78, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull78);

            //9to RENGLON "Final"
            //Titulos de Celdas
            PdfPCell Cell19 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[8].sDescripcion), fontText)); //final
            ConfCelda.PropiedadesCeldadas(Cell19,  0, 0, 0, 0, 0, 0, 1);
            tablaDetalleEdoFondo.AddCell(Cell19);

            PdfPCell Cell29 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[8].iImporte), fontText)); //final
            ConfCelda.PropiedadesCeldadas(Cell29, 0, 0, 0, 0, 0, 0, 2);
            tablaDetalleEdoFondo.AddCell(Cell29);

            PdfPCell cellnull19 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull19, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull19);

            PdfPCell cellnull29 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull29, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull29);

            PdfPCell cellnull39 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull39, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull39);

            PdfPCell cellnull49 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull49, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull49);

            PdfPCell cellnull59 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull59, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull59);

            PdfPCell cellnull69 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull69, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull69);

            PdfPCell cellnull79= new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellnull79, 0, 0, 0, 0, 0, 0, 0);
            tablaDetalleEdoFondo.AddCell(cellnull79);

            document.Add(tablaDetalleEdoFondo); //Finaliza el detalle de la tabla

            paragraph.Clear();
            paragraph.Add("\n");
            document.Add(paragraph);

            PdfPCell CeldaTituloCredito = new PdfPCell(new Phrase("MARGEN CREDITO: ", fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaTituloCredito,  0, 0, 0, 0, 0, 0, 1);
            tablaMargenes.AddCell(CeldaTituloCredito);

            PdfPCell CeldaCredito = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[24].iImporte), fontTitle)); //final
            ConfCelda.PropiedadesCeldadas(CeldaCredito, 0, 0, 0, 0, 0, 0, 3);
            tablaMargenes.AddCell(CeldaCredito);

            PdfPCell CeldaTituloContado = new PdfPCell(new Phrase("MARGEN CONTADO: ", fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaTituloContado,  0, 0, 0, 0, 0, 0, 1);
            tablaMargenes.AddCell(CeldaTituloContado);

            PdfPCell CeldaContado = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[25].iImporte), fontTitle)); //final
            ConfCelda.PropiedadesCeldadas(CeldaContado, 0, 0, 0, 0, 0, 0, 3);
            tablaMargenes.AddCell(CeldaContado);

            document.Add(tablaMargenes);

            if (iNumeroSeguro > 0) //club de proteccion
            {
                if (iNumeroSeguro == 1) 
                {
                    sPaquetes = "PAQUETE";
                }
                else
                {
                    sPaquetes = "PAQUETES";
                }

                paragraph.Clear();
                paragraph.Add("\n\n");
                document.Add(paragraph);

                paragraph.Clear();
                paragraph.Add("\n\n");
                document.Add(paragraph);

                paragraph.Clear();
                paragraph.Alignment = Element.ALIGN_CENTER;
                paragraph.Add(String.Format("USTED ESTA EN EL CLUB DE PROTECCION (" + iNumeroSeguro + " " + sPaquetes +") " + iImporteSeguro));
                document.Add(paragraph);

            }

            paragraph.Clear();
            paragraph.Add("\n\n");
            document.Add(paragraph);

            paragraph.Clear();
            paragraph.Add("\n\n");
            document.Add(paragraph);

            //FAER
            //TITULO
            if (listaDetalleFondoEmpleado[34].iImporte != 0) //movimiento 180
            {
                PdfPCell CeldaFaer = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(CeldaFaer, 0, 0, 0, 0, 0, 0, 2);
                tablafaer.AddCell(CeldaFaer);

                PdfPCell CeldaFaerEmpresa = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(CeldaFaerEmpresa, 0, 0, 0, 0, 0, 0, 2);
                tablafaer.AddCell(CeldaFaerEmpresa);

                PdfPCell CeldaFaerTrabajador = new PdfPCell(new Phrase("FAER", fontTitle));
                ConfCelda.PropiedadesCeldadas(CeldaFaerTrabajador, 0, 0, 0, 0, 0, 0, 3);
                tablafaer.AddCell(CeldaFaerTrabajador);
            }
            else
            {
                PdfPCell cell = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell);

                PdfPCell cell2 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell2, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell2);

                PdfPCell cell3 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell3, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell3);
            }

            PdfPCell cellfaer1 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellfaer1, 0, 0, 0, 0, 0, 0, 0);
            tablafaer.AddCell(cellfaer1);

            PdfPCell cellfaer2 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellfaer2, 0, 0, 0, 0, 0, 0, 0);
            tablafaer.AddCell(cellfaer2);

            //TITULO 2
            if (listaDetalleFondoEmpleado[34].iImporte != 0) //movimiento 180
            {
                PdfPCell CeldaFaer = new PdfPCell(new Phrase(""));
                ConfCelda.PropiedadesCeldadas(CeldaFaer, 0, 0, 0, 0, 0, 0, 2);
                tablafaer.AddCell(CeldaFaer);

                PdfPCell CeldaFaerEmpresa = new PdfPCell(new Phrase("FDO EMPRESA", fontTitle));
                ConfCelda.PropiedadesCeldadas(CeldaFaerEmpresa, 0, 0, 0, 0, 0, 0, 2);
                tablafaer.AddCell(CeldaFaerEmpresa);

                PdfPCell CeldaFaerTrabajador = new PdfPCell(new Phrase("FDO TRABAJADOR", fontTitle));
                ConfCelda.PropiedadesCeldadas(CeldaFaerTrabajador, 0, 0, 0, 0, 0, 0, 2);
                tablafaer.AddCell(CeldaFaerTrabajador);
            }
            else
            {
                PdfPCell cell = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell);

                PdfPCell cell2 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell2, 0, 0, 0, 0, 0, 0, 0); 
                tablafaer.AddCell(cell2);

                PdfPCell cell3 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell3, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell3);
            }

            PdfPCell celfaer12 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(celfaer12, 0, 0, 0, 0, 0, 0, 0);
            tablafaer.AddCell(celfaer12);

            PdfPCell celfaer22 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(celfaer22, 0, 0, 0, 0, 0, 0, 0);
            tablafaer.AddCell(celfaer22);

            //INICIAL
            if (listaDetalleFondoEmpleado[34].iImporte != 0) //movimiento 180 Faer
            {
                PdfPCell cell8 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[31].sDescripcion), fontText)); //FAER
                ConfCelda.PropiedadesCeldadas(cell8,  0, 0, 0, 0, 0, 0, 1);
                tablafaer.AddCell(cell8);

                PdfPCell cell9 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[31].iImporte), fontText)); //Empresa
                ConfCelda.PropiedadesCeldadas(cell9, 0, 0, 0, 0, 0, 0, 2);
                tablafaer.AddCell(cell9);

                PdfPCell cell10 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[35].iImporte), fontText)); //Trabajador
                ConfCelda.PropiedadesCeldadas(cell10, 0, 0, 0, 0, 0, 0, 2);
                tablafaer.AddCell(cell10);
            }
            else
            {
                PdfPCell cell = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell);

                PdfPCell cell2 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell2, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell2);

                PdfPCell cell3 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell3, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell3);
            }

            PdfPCell cellfaer31 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellfaer31, 0, 0, 0, 0, 0, 0, 0);
            tablafaer.AddCell(cellfaer31);

            PdfPCell cellfaer32 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellfaer32, 0, 0, 0, 0, 0, 0, 0);
            tablafaer.AddCell(cellfaer32);

            //APORTACIONES
            if (listaDetalleFondoEmpleado[34].iImporte != 0) //movimiento 180 Faer
            {
                PdfPCell cell8 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[32].sDescripcion), fontText)); //APORTACION
                ConfCelda.PropiedadesCeldadas(cell8,  0, 0, 0, 0, 0, 0, 1);
                tablafaer.AddCell(cell8);

                PdfPCell cell9 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[32].iImporte), fontText));
                ConfCelda.PropiedadesCeldadas(cell9, 0, 0, 0, 0, 0, 0, 2);
                tablafaer.AddCell(cell9);

                PdfPCell cell10 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[36].iImporte), fontText));
                ConfCelda.PropiedadesCeldadas(cell10, 0, 0, 0, 0, 0, 0, 2);
                tablafaer.AddCell(cell10);
            }
            else
            {
                PdfPCell cell = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell);

                PdfPCell cell2 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell2, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell2);

                PdfPCell cell3 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell3, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell3);
            }

            PdfPCell cellfaer41 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellfaer41, 0, 0, 0, 0, 0, 0, 0);
            tablafaer.AddCell(cellfaer41);

            PdfPCell cellfaer42 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellfaer42, 0, 0, 0, 0, 0, 0, 0);
            tablafaer.AddCell(cellfaer42);

            //RETIROS
            if (listaDetalleFondoEmpleado[34].iImporte != 0) //movimiento 180 Faer
            {
                PdfPCell cell8 = new PdfPCell(new Phrase(String.Format("{0:N}", "- RETIROS "), fontText)); //FAER
                ConfCelda.PropiedadesCeldadas(cell8,  0, 0, 0, 0, 0, 0, 1);
                tablafaer.AddCell(cell8);

                PdfPCell cell9 = new PdfPCell(new Phrase(String.Format("{0:N}", 0.00), fontText)); //RETIROS
                ConfCelda.PropiedadesCeldadas(cell9, 0, 0, 0, 0, 0, 0, 2);
                tablafaer.AddCell(cell9);

                PdfPCell cell10 = new PdfPCell(new Phrase(String.Format("{0:N}", 0.00), fontText)); //RETIROS
                ConfCelda.PropiedadesCeldadas(cell10, 0, 0, 0, 0, 0, 0, 2);
                tablafaer.AddCell(cell10);
            }
            else
            {
                PdfPCell cell = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell);

                PdfPCell cell2 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell2, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell2);

                PdfPCell cell3 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell3, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell3);
            }

            PdfPCell cellfaer51 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellfaer51, 0, 0, 0, 0, 0, 0, 0);
            tablafaer.AddCell(cellfaer51);

            PdfPCell cellfaer52 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellfaer52, 0, 0, 0, 0, 0, 0, 0);
            tablafaer.AddCell(cellfaer52);

            //INTERESES
            if (listaDetalleFondoEmpleado[34].iImporte != 0) //movimiento 180 Faer
            {
                PdfPCell cell8 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[33].sDescripcion), fontText)); //FAER
                ConfCelda.PropiedadesCeldadas(cell8,  0, 0, 0, 0, 0, 0, 1);
                tablafaer.AddCell(cell8);

                PdfPCell cell9 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[33].iImporte), fontText)); //Intereses
                ConfCelda.PropiedadesCeldadas(cell9, 0, 0, 0, 0, 0, 0, 2);
                tablafaer.AddCell(cell9);

                PdfPCell cell10 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[37].iImporte), fontText)); //Intereses
                ConfCelda.PropiedadesCeldadas(cell10, 0, 0, 0, 0, 0, 0, 2);
                tablafaer.AddCell(cell10);
            }
            else
            {
                PdfPCell cell = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell);

                PdfPCell cell2 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell2, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell2);

                PdfPCell cell3 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell3, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell3);
            }

            PdfPCell cellfaer61 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellfaer61, 0, 0, 0, 0, 0, 0, 0);
            tablafaer.AddCell(cellfaer61);

            PdfPCell cellfaer62 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellfaer62, 0, 0, 0, 0, 0, 0, 0);
            tablafaer.AddCell(cellfaer62);

            //FINAL
            if (listaDetalleFondoEmpleado[34].iImporte != 0) //movimiento 180 Faer
            {
                PdfPCell cell8 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[34].sDescripcion), fontText)); //FAER
                ConfCelda.PropiedadesCeldadas(cell8,  0, 0, 0, 0, 0, 0, 1);
                tablafaer.AddCell(cell8);

                PdfPCell cell9 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[34].iImporte), fontText)); //FINAL
                ConfCelda.PropiedadesCeldadas(cell9, 0, 0, 0, 0, 0, 0, 2);
                tablafaer.AddCell(cell9);

                PdfPCell cell10 = new PdfPCell(new Phrase(String.Format("{0:N}", listaDetalleFondoEmpleado[38].iImporte), fontText)); //FINAL
                ConfCelda.PropiedadesCeldadas(cell10, 0, 0, 0, 0, 0, 0, 2);
                tablafaer.AddCell(cell10);
            }
            else
            {
                PdfPCell cell = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell);

                PdfPCell cell2 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell2, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell2);

                PdfPCell cell3 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
                ConfCelda.PropiedadesCeldadas(cell3, 0, 0, 0, 0, 0, 0, 0);
                tablafaer.AddCell(cell3);
            }

            PdfPCell cellfaer71 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellfaer71, 0, 0, 0, 0, 0, 0, 0);
            tablafaer.AddCell(cellfaer71);

            PdfPCell cellfaer72 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cellfaer72, 0, 0, 0, 0, 0, 0, 0);
            tablafaer.AddCell(cellfaer72);

            document.Add(tablafaer);

            document.Close();
        }
    }
}
