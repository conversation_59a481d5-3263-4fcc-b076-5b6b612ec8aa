﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Entidades.Nomina;
using System.Configuration;
using System.IO;
using iTextSharp.text;
using iTextSharp.text.pdf;
using System.Windows.Forms;
using Datos.PDF;
using Entidades.PDFNomina;
using Negocios.Generales;

namespace Negocios.PDF
{
    public class PDFUtilidadesBO
    {
        PDFNominaConfiguracion pdfNominaConfiguracion = null;

        public void construirPDFUtilidades(DateTime dFechaNomina, int iCentro, string sCentro, string  dTotalAPagar, string dTotalIngresos, string dTotalEgresos,
            DateTime dFechaFin, string sNumCuenta, string sRutaPago, string sDescCiudad,
            int iNumEmp, string sApellidoPaterno, string sApellidoMaterno, string sNombre, string sNSS,
            string sRFC, string sCURP, List<NominaDetalleEmpleado> listaNominaDetalle, int iNumEmpresa, string sNombreEmpresa)
        {
            Document document = new Document(PageSize.LETTER); //Tamaño de Hoja LETTER = CARTA
            string sRutaCarpeta = @"C:\PDF\"; //Se obtiene la ruta donde se generara el PDF
            //string sRutaCarpeta = ConfigurationManager.AppSettings["CarpetaPDF"].ToString(); //Se obtiene la ruta donde se generara el PDF
            bool bExisteCarpeta = Directory.Exists(sRutaCarpeta);
            string sRutaCompleta = "" + sRutaCarpeta + "" + iNumEmp.ToString() + "_" + dFechaNomina.ToString("yyyy-MM-dd") + ".pdf";
            string sNombreArchivoLogo = string.Empty;
            const string sRutaLogoPermitida = @"C:\sys\progs\";
            int iAnchoLogo = 0;
            int iAltoLogo = 0;
            string sTotalAPagar = string.Empty;
            string sTotalIngresos = string.Empty;
            string sTotalEgresos = string.Empty;
            string sAutor = string.Empty;
            float[] anchoDeColumnas = new float[] { 40f, 10f, 10f }; //Se genera variable para ancho de las columnas
            iTextSharp.text.Font fontTitle = FontFactory.GetFont("Arial", 9);
            iTextSharp.text.Font fontText = FontFactory.GetFont("Arial", 8);

            if (bExisteCarpeta == false)
            {
                Directory.CreateDirectory(sRutaCarpeta);
            }

            GeneralesBO generalesBO = new GeneralesBO();

            if (pdfNominaConfiguracion == null || pdfNominaConfiguracion.iNumEmpresa != iNumEmpresa)
            {
                PDFDAL pdfDAL = new PDFDAL();
                pdfNominaConfiguracion = pdfDAL.ObtenerPDFNominaConfiguracion(iNumEmpresa);
            }

            if (pdfNominaConfiguracion != null)
            {
                sNombreArchivoLogo = pdfNominaConfiguracion.sNombreArchivo;
                iAnchoLogo = pdfNominaConfiguracion.iAncho;
                iAltoLogo = pdfNominaConfiguracion.iAlto;
                sAutor = pdfNominaConfiguracion.sAutor;
            }

            bool bExisteLogo = File.Exists(Path.Combine(sRutaLogoPermitida, sNombreArchivoLogo)); //Verifica que exista el logotipo en la ruta especificada

            if (bExisteLogo == false)
            {
                MessageBox.Show("Es necesario que exista el Logotipo en la siguiente ruta: " + Path.Combine(sRutaLogoPermitida, sNombreArchivoLogo) + "", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Environment.Exit(1); //Cierra la aplicacion
            }

            PdfWriter.GetInstance(document, new FileStream(sRutaCompleta, FileMode.Create));

            //Configuracion del PDF 
            document.AddTitle("Comprobante de Utilidades");
            document.AddAuthor(sAutor);
            document.Open();

            iTextSharp.text.Image imgLogo = iTextSharp.text.Image.GetInstance(Path.Combine(sRutaLogoPermitida, sNombreArchivoLogo)); //Dirreccion a la imagen que se hace referencia
            //imgLogo.SetAbsolutePosition(0, 0); //Posicion en el eje carteciano de X y Y
            imgLogo.ScaleAbsolute(iAnchoLogo, iAltoLogo);//Ancho y altura de la imagen
            imgLogo.Alignment = Element.ALIGN_RIGHT;
            document.Add(imgLogo); // Agrega la imagen al documento

            Paragraph paragraph = new Paragraph();
            paragraph.Clear();
            paragraph.Alignment = Element.ALIGN_LEFT;
            paragraph.SetLeading(0.0f, 1.2f);
            paragraph.Font = fontText;
            paragraph.Add("\n");
            document.Add(paragraph);

            paragraph.Clear();
            paragraph.Add(Convert.ToString(iCentro) + " " + sCentro + " " + dFechaNomina.ToString("yyyy-MM-dd"));
            document.Add(paragraph);

            paragraph.Clear();
            paragraph.Add("\n");
            document.Add(paragraph);

            sTotalAPagar = String.Format("{0:C}", dTotalAPagar);

            paragraph.Clear();
            paragraph.Add("RECIBI DE " + sNombreEmpresa + " EL REPARTO DE UTILIDADES CORRESPONDIENTE AL PERIODO DEL 01-ENE AL 31-DIC COMO SIGUE:");
            document.Add(paragraph);

            paragraph.Clear();
            paragraph.Add("NUM. DE CUENTA: " + sNumCuenta);
            document.Add(paragraph);

            paragraph.Clear();
            paragraph.Add(sRutaPago);
            document.Add(paragraph);

            paragraph.Clear();
            paragraph.Add("\n"); //Para agregar un salto de linea 
            document.Add(paragraph);

            //Generacion del Detalle de la Nomina 
            PdfPTable tablaDetalleNomina = new PdfPTable(3);
            tablaDetalleNomina.SetWidths(anchoDeColumnas);

            //Titulos de Celdas
            PdfPCell CeldaTituloMovimiento = new PdfPCell(new Phrase(" "));
            CeldaTituloMovimiento.BorderWidthTop = 1;
            CeldaTituloMovimiento.BorderWidthLeft = 1;
            CeldaTituloMovimiento.BorderWidthRight = 0;
            CeldaTituloMovimiento.BorderWidthBottom = 0;
            tablaDetalleNomina.AddCell(CeldaTituloMovimiento);

            PdfPCell CeldaTituloPersepcion = new PdfPCell(new Phrase("Ingresos", fontTitle));
            CeldaTituloPersepcion.HorizontalAlignment = Element.ALIGN_RIGHT;
            CeldaTituloPersepcion.BorderWidthTop = 1;
            CeldaTituloPersepcion.BorderWidthLeft = 0;
            CeldaTituloPersepcion.BorderWidthRight = 0;
            CeldaTituloPersepcion.BorderWidthBottom = 0;
            CeldaTituloPersepcion.PaddingRight = 10;
            tablaDetalleNomina.AddCell(CeldaTituloPersepcion);

            PdfPCell CeldaTituloDeduccion = new PdfPCell(new Phrase("Egresos", fontTitle));
            CeldaTituloDeduccion.HorizontalAlignment = Element.ALIGN_RIGHT;
            CeldaTituloDeduccion.BorderWidthTop = 1;
            CeldaTituloDeduccion.BorderWidthLeft = 0;
            CeldaTituloDeduccion.BorderWidthRight = 1;
            CeldaTituloDeduccion.BorderWidthBottom = 0;
            CeldaTituloDeduccion.PaddingRight = 10;
            tablaDetalleNomina.AddCell(CeldaTituloDeduccion);

            for (int i = 0; i < listaNominaDetalle.LongCount(); i++)
            {
                //Se agrega movimiento
                if (listaNominaDetalle[i].sPercepcionDeduccion == "P")
                {
                    PdfPCell CeldaMovimientoP = new PdfPCell(new Phrase(listaNominaDetalle[i].sDescripcionMovimiento, fontText));
                    CeldaMovimientoP.HorizontalAlignment = Element.ALIGN_LEFT;
                    CeldaMovimientoP.BorderWidthTop = 0;
                    CeldaMovimientoP.BorderWidthLeft = 1;
                    CeldaMovimientoP.BorderWidthRight = 0;
                    CeldaMovimientoP.BorderWidthBottom = 0;
                    CeldaMovimientoP.PaddingLeft = 10;
                    tablaDetalleNomina.AddCell(CeldaMovimientoP);
                }
                else
                {
                    PdfPCell CeldaMovimientoD = new PdfPCell(new Phrase("- " + listaNominaDetalle[i].sDescripcionMovimiento, fontText));
                    CeldaMovimientoD.HorizontalAlignment = Element.ALIGN_LEFT;
                    CeldaMovimientoD.BorderWidthTop = 0;
                    CeldaMovimientoD.BorderWidthLeft = 1;
                    CeldaMovimientoD.BorderWidthRight = 0;
                    CeldaMovimientoD.BorderWidthBottom = 0;
                    CeldaMovimientoD.PaddingLeft = 10;
                    tablaDetalleNomina.AddCell(CeldaMovimientoD);
                }

                //Se agrega Persepccion
                if (listaNominaDetalle[i].sPercepcionDeduccion == "P")
                {
                    PdfPCell CeldaImporteP = new PdfPCell(new Phrase(String.Format("{0:C}", listaNominaDetalle[i].dImporte), fontText));
                    CeldaImporteP.HorizontalAlignment = Element.ALIGN_RIGHT;
                    CeldaImporteP.BorderWidthTop = 0;
                    CeldaImporteP.BorderWidthLeft = 0;
                    CeldaImporteP.BorderWidthRight = 0;
                    CeldaImporteP.BorderWidthBottom = 0;
                    CeldaImporteP.PaddingRight = 10;
                    tablaDetalleNomina.AddCell(CeldaImporteP);
                }
                else
                {
                    PdfPCell cell = new PdfPCell(new Phrase(" "));
                    cell.BorderWidthTop = 0;
                    cell.BorderWidthLeft = 0;
                    cell.BorderWidthRight = 0;
                    cell.BorderWidthBottom = 0;
                    cell.PaddingRight = 10;
                    tablaDetalleNomina.AddCell(cell);
                }

                //Se agrega Deduccion
                if (listaNominaDetalle[i].sPercepcionDeduccion == "D")
                {
                    PdfPCell CeldaImporteD = new PdfPCell(new Phrase(String.Format("{0:C}", listaNominaDetalle[i].dImporte), fontText));
                    CeldaImporteD.HorizontalAlignment = Element.ALIGN_RIGHT;
                    CeldaImporteD.BorderWidthTop = 0;
                    CeldaImporteD.BorderWidthLeft = 0;
                    CeldaImporteD.BorderWidthRight = 1;
                    CeldaImporteD.BorderWidthBottom = 0;
                    CeldaImporteD.PaddingRight = 10;
                    tablaDetalleNomina.AddCell(CeldaImporteD);
                }
                else
                {
                    PdfPCell cell = new PdfPCell(new Phrase(" "));
                    cell.BorderWidthTop = 0;
                    cell.BorderWidthLeft = 0;
                    cell.BorderWidthRight = 1;
                    cell.BorderWidthBottom = 0;
                    cell.PaddingRight = 10;
                    tablaDetalleNomina.AddCell(cell);
                }
            }

            sTotalIngresos = String.Format("{0:C}", dTotalIngresos);
            sTotalEgresos = String.Format("{0:C}", dTotalEgresos);

            //Agregar totales Ingresos - Egresos
            PdfPCell CeldaTotales = new PdfPCell(new Phrase("TOTALES", fontTitle));
            CeldaTotales.BorderWidthTop = 0;
            CeldaTotales.BorderWidthLeft = 1;
            CeldaTotales.BorderWidthRight = 0;
            CeldaTotales.BorderWidthBottom = 0;
            CeldaTotales.PaddingLeft = 10;
            tablaDetalleNomina.AddCell(CeldaTotales);

            PdfPCell CeldaTotalIngresos = new PdfPCell(new Phrase(sTotalIngresos, fontTitle));
            CeldaTotalIngresos.HorizontalAlignment = Element.ALIGN_RIGHT;
            CeldaTotalIngresos.BorderWidthTop = 0;
            CeldaTotalIngresos.BorderWidthLeft = 0;
            CeldaTotalIngresos.BorderWidthRight = 0;
            CeldaTotalIngresos.BorderWidthBottom = 0;
            CeldaTotalIngresos.PaddingRight = 10;
            tablaDetalleNomina.AddCell(CeldaTotalIngresos);

            PdfPCell CeldaTotalEgresos = new PdfPCell(new Phrase(sTotalEgresos, fontTitle));
            CeldaTotalEgresos.HorizontalAlignment = Element.ALIGN_RIGHT;
            CeldaTotalEgresos.BorderWidthTop = 0;
            CeldaTotalEgresos.BorderWidthLeft = 0;
            CeldaTotalEgresos.BorderWidthRight = 1;
            CeldaTotalEgresos.BorderWidthBottom = 0;
            CeldaTotalEgresos.PaddingRight = 10;
            tablaDetalleNomina.AddCell(CeldaTotalEgresos);

            //Agrega total a pagar 

            PdfPCell CeldaAPagar = new PdfPCell(new Phrase("NETO", fontTitle));
            CeldaAPagar.BorderWidthTop = 0;
            CeldaAPagar.BorderWidthLeft = 1;
            CeldaAPagar.BorderWidthRight = 0;
            CeldaAPagar.BorderWidthBottom = 1;
            CeldaAPagar.PaddingLeft = 10;
            tablaDetalleNomina.AddCell(CeldaAPagar);

            PdfPCell CeldaTotalAPagar = new PdfPCell(new Phrase(sTotalAPagar, fontTitle));
            CeldaTotalAPagar.HorizontalAlignment = Element.ALIGN_RIGHT;
            CeldaTotalAPagar.BorderWidthTop = 0;
            CeldaTotalAPagar.BorderWidthLeft = 0;
            CeldaTotalAPagar.BorderWidthRight = 0;
            CeldaTotalAPagar.BorderWidthBottom = 1;
            CeldaTotalAPagar.PaddingRight = 10;
            tablaDetalleNomina.AddCell(CeldaTotalAPagar);

            PdfPCell Celda = new PdfPCell(new Phrase(" "));
            Celda.HorizontalAlignment = Element.ALIGN_LEFT;
            Celda.BorderWidthTop = 0;
            Celda.BorderWidthLeft = 0;
            Celda.BorderWidthRight = 1;
            Celda.BorderWidthBottom = 1;
            Celda.PaddingRight = 10;
            tablaDetalleNomina.AddCell(Celda);

            document.Add(tablaDetalleNomina); //Finaliza el detalle de la tabla

            paragraph.Clear();
            paragraph.Add("\n");
            document.Add(paragraph);

            paragraph.Clear();
            paragraph.Alignment = Element.ALIGN_LEFT;
            paragraph.Add(sDescCiudad + " " + dFechaFin.ToString("yyyy-MM-dd"));
            document.Add(paragraph);

            paragraph.Clear();
            paragraph.Add(Convert.ToString(iNumEmp) + " " + sApellidoPaterno + " " + sApellidoMaterno + " " + sNombre);
            document.Add(paragraph);

            paragraph.Clear();
            paragraph.Add("NSS: " + sNSS);
            document.Add(paragraph);

            paragraph.Clear();
            paragraph.Add("RFC: " + sRFC);
            document.Add(paragraph);

            paragraph.Clear();
            paragraph.Add("CURP: " + sCURP);
            document.Add(paragraph);

            document.Close();
        }
    }
}
