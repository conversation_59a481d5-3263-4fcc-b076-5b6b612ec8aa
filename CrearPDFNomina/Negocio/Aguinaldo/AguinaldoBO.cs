﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Entidades.Nomina;
using Negocios.PDF;
using Negocios.Generales;
using Datos.Nomina;
using Datos.Errores;
using Datos.Control;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Negocios.Aguinaldo
{
    public class AguinaldoBO
    {
        private static object threadLock = new object();
        public bool construirPDF(DateTime dFechaAguinaldo, int iTipo)
        {
            AguinaldoDetallesBO AguinaldoDetalles = new AguinaldoDetallesBO();
            GeneralesBO Generales = new GeneralesBO();
            PDFAguinaldoBO PDF = new PDFAguinaldoBO();

            bool bGenero = true;
            byte[] bPDF64 = null;
            int iContador = 0;

            try
            {
                List<NominaEmpleado> listaAguinaldo = obtenerNominaEmpleado(dFechaAguinaldo);

                foreach (var NominaEmpeados in listaAguinaldo)
                {
                    List<NominaDetalleEmpleado> listaAguinaldoDetalle = AguinaldoDetalles.obtenerAguinaldoDetalleEmpleado(NominaEmpeados.iNumEmp, dFechaAguinaldo, NominaEmpeados.iNumeroEmpresa);

                    if (listaAguinaldoDetalle.LongCount() >= 1)
                    {
                        iContador++;
                        PDF.construirPDFAguinaldo(dFechaAguinaldo, NominaEmpeados.iCentro, NominaEmpeados.sDescCentro, NominaEmpeados.dTotalAPagar, NominaEmpeados.dTotalIngresos, NominaEmpeados.dTotalEgresos, NominaEmpeados.dFechaFinal, NominaEmpeados.sTarjetaBanco, NominaEmpeados.sDescRutaPago, NominaEmpeados.sDescCiudad, NominaEmpeados.iNumEmp, NominaEmpeados.sApellidoPaterno, NominaEmpeados.sApellidoMaterno, NominaEmpeados.sNombre, NominaEmpeados.sNumAfiliacion, NominaEmpeados.sRFC, NominaEmpeados.sCURP, listaAguinaldoDetalle, NominaEmpeados.iNumeroEmpresa, NominaEmpeados.sNombreEmpresa);
                        bPDF64 = Generales.convertirPDFA64(NominaEmpeados.iNumEmp, dFechaAguinaldo); //Se codifica PDF a 64 byte para insertarlo en BD nominacontabilidad
                        guardarTalonEmpleado(iTipo, NominaEmpeados.iNumEmp, NominaEmpeados.sApellidoPaterno, NominaEmpeados.sApellidoMaterno, NominaEmpeados.sNombre, bPDF64, dFechaAguinaldo, NominaEmpeados.sCorreoEmpleado, NominaEmpeados.iNumeroEmpresa); //sCemail
                        //Generales.decodificarPDF(listaAguinaldo[i].iNumEmp,dFechaAguinaldo); //EXTRA PARA DECODIFICAR PDF DESDE LA BASE DE DATOS

                        //Depura archivo PDF del disco local 
                        Generales.depurarArchivo(NominaEmpeados.iNumEmp, dFechaAguinaldo);
                    }
                }

                if (iContador == 0)
                {
                    //MessageBox.Show("No existen registros para generar los PDF's", "PDF Nomina", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    bGenero = false;
                }
                else
                {
                    //Elimina Carpeta de PDF's del disco local
                    Generales.depurarCarpeta();
                }
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Aguinaldo", "AguinaldoBO.cs", "construirPDF", "Error al obtener al construir el PDF", 16, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }

            return bGenero;
        }

        public List<NominaEmpleado> obtenerNominaEmpleado(DateTime dFechaAguinaldo)
        {
            NominaDAL Nomina = new NominaDAL();
            try
            {
                return Nomina.consultarNomina(dFechaAguinaldo);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Aguinaldo", "AguinaldoBO.cs", "obtenerNominaEmpleado", "Error al obtener informacion de los empleados", 17, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public Int64 guardarTalonEmpleado(int iTipo, int iNumeroEmpleado, string sApellidoPaterno, string ApellidoMaterno, string sNombre,
            byte[] sPDF64, DateTime dFechaAguinaldo, string sCorreoEmpleado, int iEmpresa)
        {
            NominaDAL Nomina = new NominaDAL();

            try
            {
                return Nomina.guardarTalonNomina(iTipo, iNumeroEmpleado, sApellidoPaterno, ApellidoMaterno, sNombre, sPDF64, dFechaAguinaldo, sCorreoEmpleado, iEmpresa);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Aguinaldo", "AguinaldoBO.cs", "guardarTalonEmpleado", "Error al guardar informacion del empleado", 18, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }
    }
}
