﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Datos.Fondo;
using Datos.Errores;
using Entidades.Fondo;

namespace Negocios.Fondo
{
    public class FondoDetalleBO
    {
        public List<EstadoDetalleFondo> obtenerEdoFondoDetalleEmpleados(int iNumeroEmpleado, DateTime dFechaEdoFondo)
        {
            FondoDetallesDAL FondoDetalles = new FondoDetallesDAL();
            try
            {
                return FondoDetalles.consultarEdoFondoDetalle(iNumeroEmpleado, dFechaEdoFondo);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Fondo", "FondoDetallesBO.cs", "obtenerEdoFondoDetalleEmpleados", "Error al obtener el detalle del fondo del empleado", 26, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }
    }
}
