﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Datos.Fondo;
using Datos.Control;
using Datos.Nomina;
using Datos.Errores;
using Entidades.Fondo;
using Negocios.PDF;
using Negocios.Generales;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Negocios.Fondo
{
    public class FondoBO
    {
        private static object threadLock = new object();
        public bool construirPDF(DateTime dFechaEdoFondo, int iTipo)
        {
            FondoDetalleBO FondoEdoDetalle = new FondoDetalleBO();
            PDFFondoBO PDF = new PDFFondoBO();
            GeneralesBO Generales = new GeneralesBO();

            bool bGenero = true;
            byte[] bPDF64 = null;
            int iContador = 0;

            #region ValidacionCorreos
            /*JObject ConsultaCorreo = new JObject();
            string sCemail, sValorRetorno = string.Empty;*/

            /*try
            {

                List<EstadoFondo> listaFondo = obtenerEdoFondoEmpleado(dFechaEdoFondo);
                ValidacionCorreoDAL CorreoCoppel = new ValidacionCorreoDAL();
                ControlHilos Hilos = new ControlHilos();

                iNumHilos = Hilos.ObtenerNumHilos(iTipo);
                //for (int i = 0; i < listaFondo.LongCount(); i++)
                listaFondo.EachParallel(iNumHilos, EstadoFondo =>
                {
                    lock (threadLock)
                    {
                        ConsultaCorreo = CorreoCoppel.consultaPorNumeroCliente(EstadoFondo.iNumEmp);
                        //Obtener resultados
                        sCemail = ConsultaCorreo["cemail"].ToString();
                        sValorRetorno = ConsultaCorreo["ivalorretorno"].ToString();

                        if (Convert.ToInt32(sValorRetorno) > 1)
                        {
                            List<EstadoDetalleFondo> listaFondoDetalle = FondoEdoDetalle.obtenerEdoFondoDetalleEmpleados(EstadoFondo.iNumEmp, dFechaEdoFondo);

                            if (listaFondoDetalle.LongCount() >= 1)
                            {
                                iContador++;
                                PDF.construirPDFFondo(dFechaEdoFondo, EstadoFondo.iNumEmp, EstadoFondo.sApellidoPaterno, EstadoFondo.sApellidoMaterno, EstadoFondo.sNombre, EstadoFondo.sRFC, EstadoFondo.sCurp, EstadoFondo.dFechaAlta, EstadoFondo.iSueldoMensual, EstadoFondo.iCentro, EstadoFondo.sDescripcionCentro, EstadoFondo.iNumeroSeguro, EstadoFondo.iImporteSeguro, listaFondoDetalle);
                                bPDF64 = Generales.convertirPDFA64(EstadoFondo.iNumEmp, dFechaEdoFondo); //Se codifica PDF a 64 byte para insertarlo en BD nominacontabilidad
                                guardarTalonEmpleado(iTipo, EstadoFondo.iNumEmp, EstadoFondo.sApellidoPaterno, EstadoFondo.sApellidoMaterno, EstadoFondo.sNombre, bPDF64, dFechaEdoFondo, sCemail);//listaFondo[i].sCorreoEmpleado
                                //Depura archivo PDF del disco local 
                                Generales.depurarArchivo(EstadoFondo.iNumEmp, dFechaEdoFondo);
                            }
                        }
                    }
                });*/
            #endregion ValidacionCorreos

            try 
	        {
                List<EstadoFondo> listaFondo = obtenerEdoFondoEmpleado(dFechaEdoFondo);

                foreach (var EstadoFondo in listaFondo)
                {
                    List<EstadoDetalleFondo> listaFondoDetalle = FondoEdoDetalle.obtenerEdoFondoDetalleEmpleados(EstadoFondo.iNumEmp, dFechaEdoFondo);

                    if (listaFondoDetalle.LongCount() >= 1)
                    {
                        iContador++;
                        PDF.construirPDFFondo(dFechaEdoFondo, EstadoFondo.iNumEmp, EstadoFondo.sApellidoPaterno, EstadoFondo.sApellidoMaterno, EstadoFondo.sNombre, EstadoFondo.sRFC, EstadoFondo.sCurp, EstadoFondo.dFechaAlta, EstadoFondo.iSueldoMensual, EstadoFondo.iCentro, EstadoFondo.sDescripcionCentro, EstadoFondo.iNumeroSeguro, EstadoFondo.iImporteSeguro, listaFondoDetalle);
                        bPDF64 = Generales.convertirPDFA64(EstadoFondo.iNumEmp, dFechaEdoFondo); //Se codifica PDF a 64 byte para insertarlo en BD nominacontabilidad
                        guardarTalonEmpleado(iTipo, EstadoFondo.iNumEmp, EstadoFondo.sApellidoPaterno, EstadoFondo.sApellidoMaterno, EstadoFondo.sNombre, bPDF64, dFechaEdoFondo, EstadoFondo.sCorreoEmpleado, 0);//sCemail
                        //Depura archivo PDF del disco local 
                        Generales.depurarArchivo(EstadoFondo.iNumEmp, dFechaEdoFondo);
                    }
                        
                }
		

                if (iContador == 0)
                {
                    //MessageBox.Show("No existen registros para generar los PDF's", "PDF Nomina", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    bGenero = false;
                }
                else
                {
                    //Elimina Carpeta de PDF's del disco local
                    Generales.depurarCarpeta();

                    //Guardar la fecha de la ultima nomina
                    GuardarFechaFondo(dFechaEdoFondo, iTipo);

                }
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Fondo", "FondoBO.cs", "construirPDF", "Error al al generar el PDF", 22, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }

            return bGenero;
        }

        public List<EstadoFondo> obtenerEdoFondoEmpleado(DateTime dFechaEdoFondo)
        {
            FondoDAL Fondo = new FondoDAL();
            try
            {
                return Fondo.consultaGeneralesFondo(dFechaEdoFondo);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Fondo", "FondoBO.cs", "obtenerEdoFondoEmpleado", "Error al obtener la informacion del empleado", 23, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public void GuardarFechaFondo(DateTime dFechaEdoFondo, int iTipo)
        {
            FondoControlDAL FondoControl = new FondoControlDAL();
            try
            {
                FondoControl.GuardarFechaControl(dFechaEdoFondo, iTipo);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Fondo", "FondoBO.cs", "GuardarFechaFondo", "Error al guardar la fecha del fondo", 24, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        // Utilizar el de nomina
        public Int64 guardarTalonEmpleado(int iTipo, int iNumeroEmpleado, string sApellidoPaterno, string ApellidoMaterno, string sNombre,
            byte[] sPDF64, DateTime dFechaNomina, string sCorreoEmpleado, int iEmpresa)
        {
            NominaDAL Nomina = new NominaDAL();

            try
            {
                return Nomina.guardarTalonNomina(iTipo, iNumeroEmpleado, sApellidoPaterno, ApellidoMaterno, sNombre, sPDF64, dFechaNomina, sCorreoEmpleado, iEmpresa);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Fondo", "FondoBO.cs", "guardarTalonEmpleado", "Error al guardar talon del empleado", 25, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }
    }
}
