﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Entidades.Nomina;
using Negocios.PDF;
using Negocios.Generales;
using Datos.Nomina;
using Datos.Errores;
using Datos.Control;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Negocios.Utilidades
{
    public class UtilidadesBO
    {
        private static object threadLock = new object();
        public bool construirPDF(DateTime dFechaNomina, int iTipo)
        {
            UtilidadesDetalleBO UtilidadesDetalle = new UtilidadesDetalleBO();
            PDFUtilidadesBO PDF = new PDFUtilidadesBO();
            GeneralesBO Generales = new GeneralesBO();

            bool bGenero = true;
            byte[] bPDF64 = null;
            int iContador = 0;

            try
            {
                List<NominaEmpleado> listaNomina = obtenerNominaEmpleado(dFechaNomina);
                
                foreach (var NominaEmpeados in listaNomina)
                {
                    List<NominaDetalleEmpleado> listaNominaDetalle = UtilidadesDetalle.obtenerNominaDetalleEmpleado(NominaEmpeados.iNumEmp, dFechaNomina, NominaEmpeados.iNumeroEmpresa);

                    if (listaNominaDetalle.LongCount() >= 1)
                    {
                        iContador++;
                        PDF.construirPDFUtilidades(dFechaNomina, NominaEmpeados.iCentro, NominaEmpeados.sDescCentro, NominaEmpeados.dTotalAPagar, NominaEmpeados.dTotalIngresos, NominaEmpeados.dTotalEgresos, NominaEmpeados.dFechaFinal, NominaEmpeados.sTarjetaBanco, NominaEmpeados.sDescRutaPago, NominaEmpeados.sDescCiudad, NominaEmpeados.iNumEmp, NominaEmpeados.sApellidoPaterno, NominaEmpeados.sApellidoMaterno, NominaEmpeados.sNombre, NominaEmpeados.sNumAfiliacion, NominaEmpeados.sRFC, NominaEmpeados.sCURP, listaNominaDetalle, NominaEmpeados.iNumeroEmpresa, NominaEmpeados.sNombreEmpresa);
                        bPDF64 = Generales.convertirPDFA64(NominaEmpeados.iNumEmp, dFechaNomina); //Se codifica PDF a 64 byte para insertarlo en BD nominacontabilidad
                        guardarTalonEmpleado(iTipo, NominaEmpeados.iNumEmp, NominaEmpeados.sApellidoPaterno, NominaEmpeados.sApellidoMaterno, NominaEmpeados.sNombre, bPDF64, dFechaNomina, NominaEmpeados.sCorreoEmpleado, NominaEmpeados.iNumeroEmpresa); //sCemail
                        //Generales.decodificarPDF(listaNomina[i].iNumEmp,dFechaNomina); //EXTRA PARA DECODIFICAR PDF DESDE LA BASE DE DATOS

                        //Depura archivo PDF del disco local 
                        Generales.depurarArchivo(NominaEmpeados.iNumEmp, dFechaNomina);
                    }
                }

                if (iContador == 0)
                {
                    //MessageBox.Show("No existen registros para generar los PDF's", "PDF Nomina", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    bGenero = false;
                }
                else
                {
                    //Elimina Carpeta de PDF's del disco local
                    Generales.depurarCarpeta();
                }
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Utilidades", "UtilidadesBO.cs", "construirPDF", "Error al Generar PDF", 38, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }

            return bGenero;
        }

        public List<NominaEmpleado> obtenerNominaEmpleado(DateTime dFechaNomina)
        {
            NominaDAL Nomina = new NominaDAL();
            try
            {
                return Nomina.consultarNomina(dFechaNomina);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Utilidades", "UtilidadesBO.cs", "obtenerNominaEmpleado", "Error al obtener la lista de empleados", 39, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public Int64 guardarTalonEmpleado(int iTipo, int iNumeroEmpleado, string sApellidoPaterno, string ApellidoMaterno, string sNombre,
           byte[] sPDF64, DateTime dFechaNomina, string sCorreoEmpleado, int iEmpresa)
        {
            NominaDAL Nomina = new NominaDAL();

            try
            {
                return Nomina.guardarTalonNomina(iTipo, iNumeroEmpleado, sApellidoPaterno, ApellidoMaterno, sNombre, sPDF64, dFechaNomina, sCorreoEmpleado, iEmpresa);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Utilidades", "UtilidadesBO.cs", "guardarTalonEmpleado", "Error al Guardar talon del empleado", 40, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }
    }
}
