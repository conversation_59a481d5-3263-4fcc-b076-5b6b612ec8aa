﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Datos.Fondo;
using Datos.Control;
using Datos.Nomina;
using Datos.Gasolina;
using Datos.Errores;
using Entidades.Gasolina;
using Negocios.PDF;
using Negocios.Generales;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Negocios.Gasolina
{
    public class GasolinaBO
    {
        private static object threadLock = new object();
        public bool construirPDF(DateTime dFechaCorte, int iTipo)
        {
            GasolinaDetallesBO GasolinaDetalle = new GasolinaDetallesBO();
            PDFGasolinaBO PDF = new PDFGasolinaBO();
            GeneralesBO Generales = new GeneralesBO();

            bool bGenero = true;
            byte[] bPDF64 = null;
            int iContador = 0;

            try
            {
                List<EstadoGasolina> listaGasolina = obtenerEdoGasolinaEmpleado(dFechaCorte);
                foreach (var EstadoGasolina in listaGasolina)
                {
                   List<EstadoDetalleGasolina> listaEmpleadosGasolina = GasolinaDetalle.obtenerEdoDetalleGasolina(EstadoGasolina.iNumEmp, dFechaCorte);

                    if (listaEmpleadosGasolina.LongCount() >= 1)
                    {
                        iContador++;
                        PDF.construirPDFGasolina(dFechaCorte, EstadoGasolina.iNumEmp, listaEmpleadosGasolina);
                        bPDF64 = Generales.convertirPDFA64(EstadoGasolina.iNumEmp, dFechaCorte); //Se codifica PDF a 64 byte para insertarlo en BD nominacontabilidad
                        guardarTalonEmpleado(iTipo, EstadoGasolina.iNumEmp, listaEmpleadosGasolina[0].sApellidoPaterno, listaEmpleadosGasolina[0].sApellidoMaterno, listaEmpleadosGasolina[0].sNombre, bPDF64, dFechaCorte, EstadoGasolina.sCorreoEmpleado, 0);
                        //Depura archivo PDF del disco local 
                        Generales.depurarArchivo(EstadoGasolina.iNumEmp, dFechaCorte);
                    }
                }

                if (iContador == 0)
                {
                    //MessageBox.Show("No existen registros para generar los PDF's", "PDF Nomina", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    bGenero = false;
                }
                else
                {
                    //Elimina Carpeta de PDF's del disco local
                    Generales.depurarCarpeta();

                    //Guardar la fecha de la ultima nomina
                    GuardarFechaControl(dFechaCorte, iTipo);

                }
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Gasolina", "GasolinaBO.cs", "construirPDF", "Error al al generar el PDF", 27, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }

            return bGenero;
        }

        public List<EstadoGasolina> obtenerEdoGasolinaEmpleado(DateTime dFechaCorte)
        {
            GasolinaDAL Gasolina = new GasolinaDAL();
            try
            {
                return Gasolina.consultarEdoGasolina(dFechaCorte);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Gasolina", "GasolinaBO.cs", "obtenerEdoFondoEmpleado", "Error al obtener la informacion del empleado", 28, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public void GuardarFechaControl(DateTime dFechaCorte, int iTipo)
        {
            FondoControlDAL FondoControl = new FondoControlDAL();
            try
            {
                FondoControl.GuardarFechaControl(dFechaCorte, iTipo);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Gasolina", "GasolinaBO.cs", "GuardarFechaFondo", "Error al guardar la fecha de gasolina", 29, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        // Utilizar el de nomina
        public Int64 guardarTalonEmpleado(int iTipo, int iNumeroEmpleado, string sApellidoPaterno, string ApellidoMaterno, string sNombre,
            byte[] sPDF64, DateTime dFechaCorte, string sCorreoEmpleado, int iEmpresa)
        {
            NominaDAL Nomina = new NominaDAL();

            try
            {
                return Nomina.guardarTalonNomina(iTipo, iNumeroEmpleado, sApellidoPaterno, ApellidoMaterno, sNombre, sPDF64, dFechaCorte, sCorreoEmpleado, iEmpresa);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Gasolina", "GasolinaBO.cs", "guardarTalonEmpleado", "Error al guardar talon del empleado", 30, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }
    }
}
