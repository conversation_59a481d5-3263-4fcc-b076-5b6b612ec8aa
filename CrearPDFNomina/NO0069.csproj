<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{36C4BB57-F31F-4988-8C1F-B75E6C283FE7}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>NO0069</RootNamespace>
    <AssemblyName>NO0069</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <TargetFrameworkProfile />
    <PublishUrl>publicar\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject>NO0069.Program</StartupObject>
  </PropertyGroup>
  <PropertyGroup />
  <ItemGroup>
    <!-- Referencias actualizadas para solucionar vulnerabilidades y compatibles con .NET Framework 4.6 -->
    <Reference Include="BouncyCastle.Crypto">
      <HintPath>LIBRERIAS_NET46\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="FILEUTILITIES">
      <HintPath>..\..\..\..\sys\progx\libcsharp\FILEUTILITIES.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp, Version=5.5.13.3, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>LIBRERIAS_NET46\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp.pdfa">
      <HintPath>..\..\..\..\sys\progx\libcsharp\itextsharp.pdfa.dll</HintPath>
    </Reference>
    <Reference Include="Mono.Security">
      <HintPath>..\..\NO0070\Datos\Librerias\Mono.Security.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.3, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>LIBRERIAS_NET46\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Npgsql, Version=4.1.13, Culture=neutral, PublicKeyToken=5d8b90d52f46fda7, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>LIBRERIAS_NET46\Npgsql.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="UTILERIASGENERALES">
      <HintPath>..\..\..\..\sys\progx\libcsharp\UTILERIASGENERALES.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Datos\Conexion\ConexionDAL.cs" />
    <Compile Include="Datos\Conexion\ConfigurarDAL.cs" />
    <Compile Include="Datos\Control\ControlHilos.cs" />
    <Compile Include="Datos\Control\FondoControlDAL.cs" />
    <Compile Include="Datos\Control\NominaControlDAL.cs" />
    <Compile Include="Datos\Correo\CatalogoCorreoDAL.cs" />
    <Compile Include="Datos\Errores\GrabarErrorDAL.cs" />
    <Compile Include="Datos\Fondo\FondoDAL.cs" />
    <Compile Include="Datos\Fondo\FondoDetallesDAL.cs" />
    <Compile Include="Datos\Gasolina\GasolinaDAL.cs" />
    <Compile Include="Datos\Gasolina\GasolinaDetallesDAL.cs" />
    <Compile Include="Datos\Generales\GeneralesDAL.cs" />
    <Compile Include="Datos\Nomina\NominaDAL.cs" />
    <Compile Include="Datos\Nomina\NominaDetalleDAL.cs" />
    <Compile Include="Datos\PDF\PDFDAL.cs" />
    <Compile Include="Entidades\Control\DynamicRow.cs" />
    <Compile Include="Entidades\Conexion\Conexion.cs" />
    <Compile Include="Entidades\Control\FondoControl.cs" />
    <Compile Include="Entidades\Control\NominaControl.cs" />
    <Compile Include="Entidades\CorreoEmpleado\CatalogoCorreo.cs" />
    <Compile Include="Entidades\Fondo\EstadoDetalleFondo.cs" />
    <Compile Include="Entidades\Fondo\EstadoFondo.cs" />
    <Compile Include="Entidades\Gasolina\EstadoDetalleGasolina.cs" />
    <Compile Include="Entidades\Gasolina\EstadoGasolina.cs" />
    <Compile Include="Entidades\Nomina\NominaDetalleEmpleado.cs" />
    <Compile Include="Entidades\Nomina\NominaEmpleado.cs" />
    <Compile Include="Entidades\PDFNomina\PDFNominaConfiguracion.cs" />
    <Compile Include="Formas\frmPDFNomina.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Formas\frmPDFNomina.Designer.cs">
      <DependentUpon>frmPDFNomina.cs</DependentUpon>
    </Compile>
    <Compile Include="Negocio\Aguinaldo\AguinaldoBO.cs" />
    <Compile Include="Negocio\Aguinaldo\AguinaldoDetallesBO.cs" />
    <Compile Include="Negocio\Conexion\ConfigurarBO.cs" />
    <Compile Include="Negocio\Correo\CatalogoCorreoBO.cs" />
    <Compile Include="Negocio\Fondo\FondoBO.cs" />
    <Compile Include="Negocio\Fondo\FondoDetalleBO.cs" />
    <Compile Include="Negocio\Gasolina\GasolinaBO.cs" />
    <Compile Include="Negocio\Gasolina\GasolinaDetallesBO.cs" />
    <Compile Include="Negocio\Generales\ExtensionesIEnumerable.cs" />
    <Compile Include="Negocio\Generales\GeneralesBO.cs" />
    <Compile Include="Negocio\Nomina\NominaBO.cs" />
    <Compile Include="Negocio\Nomina\NominaDetalleBO.cs" />
    <Compile Include="Negocio\PDF\ConfigurarCeldasPDF.cs" />
    <Compile Include="Negocio\PDF\PDFAguinaldoBO.cs" />
    <Compile Include="Negocio\PDF\PDFFondoBO.cs" />
    <Compile Include="Negocio\PDF\PDFGasolinaBO.cs" />
    <Compile Include="Negocio\PDF\PDFNominaBO.cs" />
    <Compile Include="Negocio\PDF\PDFUtilidadesBO.cs" />
    <Compile Include="Negocio\Utilidades\UtilidadesBO.cs" />
    <Compile Include="Negocio\Utilidades\UtilidadesDetalleBO.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="Formas\frmPDFNomina.resx">
      <DependentUpon>frmPDFNomina.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="app.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.0">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4 %28x86 y x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.4.5">
      <Visible>False</Visible>
      <ProductName>Windows Installer 4.5</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
      Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>