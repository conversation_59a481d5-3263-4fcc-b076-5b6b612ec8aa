#!/bin/bash

# <PERSON>ript to help update vulnerable packages in the NO0069 project
# This script will download the latest compatible versions of the vulnerable packages
# and prepare them for manual reference update

# Create directories
mkdir -p UpdatedLibraries
cd UpdatedLibraries

echo "Creating directories for updated packages..."
mkdir -p BouncyCastle
mkdir -p iTextSharp
mkdir -p Npgsql
mkdir -p NewtonsoftJson

# Download latest compatible packages
echo "Downloading latest compatible packages..."

# BouncyCastle
echo "Downloading BouncyCastle 1.9.0..."
curl -L -o BouncyCastle/BouncyCastle.1.9.0.nupkg https://www.nuget.org/api/v2/package/BouncyCastle/1.9.0
cd BouncyCastle
unzip -o BouncyCastle.1.9.0.nupkg
cd ..

# iTextSharp
echo "Downloading iTextSharp ******** (latest 5.x version)..."
curl -L -o iTextSharp/iTextSharp.********.nupkg https://www.nuget.org/api/v2/package/iTextSharp/********
cd iTextSharp
unzip -o iTextSharp.********.nupkg
cd ..

# Npgsql
echo "Downloading Npgsql 4.1.12..."
curl -L -o Npgsql/Npgsql.4.1.12.nupkg https://www.nuget.org/api/v2/package/Npgsql/4.1.12
cd Npgsql
unzip -o Npgsql.4.1.12.nupkg
cd ..

# Newtonsoft.Json
echo "Downloading Newtonsoft.Json 13.0.3..."
curl -L -o NewtonsoftJson/Newtonsoft.Json.13.0.3.nupkg https://www.nuget.org/api/v2/package/Newtonsoft.Json/13.0.3
cd NewtonsoftJson
unzip -o Newtonsoft.Json.13.0.3.nupkg
cd ..

# Extract DLLs to a common folder for easy reference
echo "Extracting DLLs to a common folder..."
mkdir -p ../UpdatedDLLs

# Copy BouncyCastle DLL
cp BouncyCastle/lib/net40/BouncyCastle.Crypto.dll ../UpdatedDLLs/

# Copy iTextSharp DLL
cp iTextSharp/lib/iTextSharp.dll ../UpdatedDLLs/

# Copy Npgsql DLL (choose the appropriate .NET Framework version)
cp Npgsql/lib/net45/Npgsql.dll ../UpdatedDLLs/

# Copy Newtonsoft.Json DLL
cp NewtonsoftJson/lib/net45/Newtonsoft.Json.dll ../UpdatedDLLs/

echo "Package downloads and extraction complete."
echo "Updated DLLs are available in the UpdatedDLLs folder."
echo ""
echo "Next steps:"
echo "1. Update the project references to point to these new DLLs"
echo "2. Test the application thoroughly after the updates"
echo "3. Refer to update_vulnerable_packages.md for more details"
