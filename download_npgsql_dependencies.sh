#!/bin/bash

# Script para descargar las dependencias requeridas por Npgsql 4.1.13 para .NET Framework 4.6.1

echo "Descargando dependencias de Npgsql 4.1.13..."

# Crear directorio para dependencias
mkdir -p NpgsqlDependencies
cd NpgsqlDependencies

# Microsoft.Bcl.AsyncInterfaces 1.1.0
echo "Descargando Microsoft.Bcl.AsyncInterfaces 1.1.0..."
curl -L -o Microsoft.Bcl.AsyncInterfaces.1.1.0.zip https://www.nuget.org/api/v2/package/Microsoft.Bcl.AsyncInterfaces/1.1.0
unzip -o Microsoft.Bcl.AsyncInterfaces.1.1.0.zip -d Microsoft.Bcl.AsyncInterfaces

# System.Memory 4.5.3
echo "Descargando System.Memory 4.5.3..."
curl -L -o System.Memory.4.5.3.zip https://www.nuget.org/api/v2/package/System.Memory/4.5.3
unzip -o System.Memory.4.5.3.zip -d System.Memory

# System.Runtime.CompilerServices.Unsafe 4.6.0
echo "Descargando System.Runtime.CompilerServices.Unsafe 4.6.0..."
curl -L -o System.Runtime.CompilerServices.Unsafe.4.6.0.zip https://www.nuget.org/api/v2/package/System.Runtime.CompilerServices.Unsafe/4.6.0
unzip -o System.Runtime.CompilerServices.Unsafe.4.6.0.zip -d System.Runtime.CompilerServices.Unsafe

# System.Text.Json 4.6.0
echo "Descargando System.Text.Json 4.6.0..."
curl -L -o System.Text.Json.4.6.0.zip https://www.nuget.org/api/v2/package/System.Text.Json/4.6.0
unzip -o System.Text.Json.4.6.0.zip -d System.Text.Json

# System.Threading.Tasks.Extensions 4.5.3
echo "Descargando System.Threading.Tasks.Extensions 4.5.3..."
curl -L -o System.Threading.Tasks.Extensions.4.5.3.zip https://www.nuget.org/api/v2/package/System.Threading.Tasks.Extensions/4.5.3
unzip -o System.Threading.Tasks.Extensions.4.5.3.zip -d System.Threading.Tasks.Extensions

# System.ValueTuple 4.5.0
echo "Descargando System.ValueTuple 4.5.0..."
curl -L -o System.ValueTuple.4.5.0.zip https://www.nuget.org/api/v2/package/System.ValueTuple/4.5.0
unzip -o System.ValueTuple.4.5.0.zip -d System.ValueTuple

# Crear directorio para los DLLs
mkdir -p ../NpgsqlDependenciesDLLs

# Copiar los DLLs necesarios para .NET Framework 4.6.1
echo "Copiando DLLs para .NET Framework 4.6.1..."

# Microsoft.Bcl.AsyncInterfaces
find Microsoft.Bcl.AsyncInterfaces -name "Microsoft.Bcl.AsyncInterfaces.dll" -path "*/net461/*" -exec cp {} ../NpgsqlDependenciesDLLs/ \;

# System.Memory
find System.Memory -name "System.Memory.dll" -path "*/net461/*" -exec cp {} ../NpgsqlDependenciesDLLs/ \;

# System.Runtime.CompilerServices.Unsafe
find System.Runtime.CompilerServices.Unsafe -name "System.Runtime.CompilerServices.Unsafe.dll" -path "*/net461/*" -exec cp {} ../NpgsqlDependenciesDLLs/ \;

# System.Text.Json
find System.Text.Json -name "System.Text.Json.dll" -path "*/net461/*" -exec cp {} ../NpgsqlDependenciesDLLs/ \;

# System.Threading.Tasks.Extensions
find System.Threading.Tasks.Extensions -name "System.Threading.Tasks.Extensions.dll" -path "*/net461/*" -exec cp {} ../NpgsqlDependenciesDLLs/ \;

# System.ValueTuple
find System.ValueTuple -name "System.ValueTuple.dll" -path "*/net461/*" -exec cp {} ../NpgsqlDependenciesDLLs/ \;

cd ..

echo "Dependencias descargadas y extraídas en NpgsqlDependenciesDLLs/"
ls -la NpgsqlDependenciesDLLs/
