# iTextSharp Migration Guide

## Option 1: Update to Latest 5.x Version (Minimal Changes)

If you want to minimize code changes while addressing some vulnerabilities, update to the latest 5.x version (********).

### Steps:

1. Replace the existing iTextSharp.dll with the latest ******** version
2. Test the application thoroughly

This approach requires minimal code changes but may not address all security vulnerabilities.

## Option 2: Migrate to iText7 (Recommended for Security)

For better security and features, migrate to iText7. This requires significant code changes but provides better long-term security.

### Steps:

1. Install the iText7 packages:
   - iText7 (core)
   - iText7.Pdfa (if using PDF/A functionality)

2. Update code to use the new API

### Common Code Migration Examples:

#### 1. Document Creation

**iTextSharp 5.x:**
```csharp
Document document = new Document(PageSize.LETTER);
PdfWriter.GetInstance(document, new FileStream(filePath, FileMode.Create));
document.Open();
document.Add(new Paragraph("Hello World"));
document.Close();
```

**iText7:**
```csharp
PdfDocument pdfDoc = new PdfDocument(new PdfWriter(filePath));
Document document = new Document(pdfDoc, PageSize.LETTER);
document.Add(new Paragraph("Hello World"));
document.Close();
```

#### 2. Adding Images

**iTextSharp 5.x:**
```csharp
iTextSharp.text.Image img = iTextSharp.text.Image.GetInstance(imagePath);
img.ScaleAbsolute(width, height);
img.Alignment = Element.ALIGN_CENTER;
document.Add(img);
```

**iText7:**
```csharp
ImageData imageData = ImageDataFactory.Create(imagePath);
Image img = new Image(imageData);
img.ScaleAbsolute(width, height);
img.SetHorizontalAlignment(HorizontalAlignment.CENTER);
document.Add(img);
```

#### 3. Creating Tables

**iTextSharp 5.x:**
```csharp
PdfPTable table = new PdfPTable(3);
table.WidthPercentage = 100;
table.AddCell("Cell 1");
table.AddCell("Cell 2");
table.AddCell("Cell 3");
document.Add(table);
```

**iText7:**
```csharp
Table table = new Table(3);
table.SetWidth(UnitValue.CreatePercentValue(100));
table.AddCell("Cell 1");
table.AddCell("Cell 2");
table.AddCell("Cell 3");
document.Add(table);
```

#### 4. Adding Fonts

**iTextSharp 5.x:**
```csharp
iTextSharp.text.Font font = FontFactory.GetFont("Arial", 12, iTextSharp.text.Font.BOLD);
document.Add(new Paragraph("Text with custom font", font));
```

**iText7:**
```csharp
PdfFont font = PdfFontFactory.CreateFont(StandardFonts.HELVETICA_BOLD);
document.Add(new Paragraph("Text with custom font").SetFont(font).SetFontSize(12));
```

### Specific Changes for NO0069 Project

Based on the code review, here are specific changes needed for the NO0069 project:

#### PDFNominaBO.cs:

**Original Code:**
```csharp
Document document = new Document(PageSize.LETTER);
string sRutaCompleta = "" + sRutaCarpeta + "" + iNumEmp.ToString() + "_" + dFechaNomina.ToString("yyyy-MM-dd") + ".pdf";
// ...
PdfWriter.GetInstance(document, new FileStream(sRutaCompleta, FileMode.Create));
document.AddTitle("Comprobante de Nomina");
document.AddAuthor(sAutor);
document.Open();

iTextSharp.text.Image imgLogo = iTextSharp.text.Image.GetInstance(Path.Combine(sRutaLogoPermitida, sNombreArchivoLogo));
imgLogo.ScaleAbsolute(iAnchoLogo, iAltoLogo);
imgLogo.Alignment = Element.ALIGN_RIGHT;
document.Add(imgLogo);
```

**iText7 Version:**
```csharp
string sRutaCompleta = "" + sRutaCarpeta + "" + iNumEmp.ToString() + "_" + dFechaNomina.ToString("yyyy-MM-dd") + ".pdf";
PdfWriter writer = new PdfWriter(sRutaCompleta);
PdfDocument pdfDoc = new PdfDocument(writer);
Document document = new Document(pdfDoc, PageSize.LETTER);

// Set document properties
pdfDoc.GetDocumentInfo().SetTitle("Comprobante de Nomina");
pdfDoc.GetDocumentInfo().SetAuthor(sAutor);

// Add logo
ImageData imageData = ImageDataFactory.Create(Path.Combine(sRutaLogoPermitida, sNombreArchivoLogo));
Image imgLogo = new Image(imageData);
imgLogo.ScaleAbsolute(iAnchoLogo, iAltoLogo);
imgLogo.SetHorizontalAlignment(HorizontalAlignment.RIGHT);
document.Add(imgLogo);
```

#### PDFUtilidadesBO.cs:

Similar changes would be needed for the PDFUtilidadesBO.cs file.

### Required Namespace Changes

When migrating to iText7, you'll need to update the namespaces:

**iTextSharp 5.x:**
```csharp
using iTextSharp.text;
using iTextSharp.text.pdf;
```

**iText7:**
```csharp
using iText.Kernel.Pdf;
using iText.Layout;
using iText.Layout.Element;
using iText.Kernel.Geom;
using iText.IO.Image;
using iText.Layout.Properties;
```

## Testing After Migration

After migrating to iText7, thoroughly test all PDF generation functionality:

1. Verify that PDFs are generated correctly
2. Check that all content (text, images, tables) appears as expected
3. Validate PDF metadata (title, author)
4. Test PDF/A compliance if used

## Resources

- [iText7 API Documentation](https://api.itextpdf.com/iText7/)
- [iText7 Migration Guide](https://kb.itextpdf.com/home/<USER>/ebooks/itext-7-converting-to-itext-7)
- [iText7 Examples](https://github.com/itext/i7js-examples)
